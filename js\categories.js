// EatMY - Categories Page Functionality

import { categories, getRestaurantsByCategory, restaurants } from './data.js';

let currentCategoryType = 'cuisine';

// Initialize categories page
document.addEventListener('DOMContentLoaded', function() {
  initializeCategoriesPage();
});

function initializeCategoriesPage() {
  initializeCategoryTabs();
  loadCategoriesGrid(currentCategoryType);
  loadFeaturedByCategory();

  console.log('Categories page initialized');
}

function initializeCategoryTabs() {
  const categoryTabs = document.querySelectorAll('.category-tab');

  categoryTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const categoryType = this.dataset.category;
      switchCategoryType(categoryType);
    });
  });
}

function switchCategoryType(categoryType) {
  // Update active tab
  document.querySelectorAll('.category-tab').forEach(tab => {
    tab.classList.remove('active');
  });
  document.querySelector(`[data-category="${categoryType}"]`).classList.add('active');

  // Update active section
  document.querySelectorAll('.category-section').forEach(section => {
    section.style.display = 'none';
  });
  document.getElementById(`${categoryType}-section`).style.display = 'block';

  currentCategoryType = categoryType;
  loadCategoriesGrid(categoryType);
}

function loadCategoriesGrid(categoryType) {
  const gridId = `${categoryType}-grid`;
  const categoriesGrid = document.getElementById(gridId);
  if (!categoriesGrid) return;

  const categoryData = categories[categoryType] || [];

  categoriesGrid.innerHTML = categoryData.map(category =>
    createCategoryCard(category, categoryType)
  ).join('');
}

function createCategoryCard(category, categoryType) {
  let restaurantCount = 0;
  let topRestaurants = [];
  let searchUrl = '';

  // Calculate restaurant count and search URL based on category type
  switch (categoryType) {
    case 'cuisine':
      restaurantCount = getRestaurantsByCategory(category.id).length;
      topRestaurants = getRestaurantsByCategory(category.id)
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 3);
      searchUrl = `search.html?cuisine=${category.id}`;
      break;
    case 'regions':
      restaurantCount = restaurants.filter(r => r.region === category.id).length;
      topRestaurants = restaurants.filter(r => r.region === category.id)
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 3);
      searchUrl = `search.html?region=${category.id}`;
      break;
    case 'occasions':
      restaurantCount = restaurants.filter(r => r.occasions && r.occasions.includes(category.id)).length;
      topRestaurants = restaurants.filter(r => r.occasions && r.occasions.includes(category.id))
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 3);
      searchUrl = `search.html?occasion=${category.id}`;
      break;
    case 'conditions':
      restaurantCount = restaurants.filter(r => r.conditions && r.conditions.includes(category.id)).length;
      topRestaurants = restaurants.filter(r => r.conditions && r.conditions.includes(category.id))
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 3);
      searchUrl = `search.html?condition=${category.id}`;
      break;
  }

  return `
    <div class="category-card-large">
      <a href="${searchUrl}" class="card" style="display: block; text-decoration: none; color: inherit; height: 100%; transition: all var(--transition-fast);">
        <div style="padding: var(--space-6); text-align: center; height: 100%; display: flex; flex-direction: column;">
          <div style="background: ${category.color}20; width: 64px; height: 64px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto var(--space-4); font-size: 28px;">
            ${category.icon}
          </div>

          <h3 style="font-size: var(--text-lg); font-weight: 600; margin-bottom: var(--space-2); color: var(--gray-900);">
            ${category.name}
          </h3>

          <p style="color: var(--gray-600); font-size: var(--text-sm); margin-bottom: var(--space-3); flex: 1;">
            ${category.description}
          </p>

          <p style="color: var(--gray-500); font-size: var(--text-sm); margin-bottom: var(--space-4);">
            ${restaurantCount} restaurants
          </p>

          ${topRestaurants.length > 0 ? `
            <div style="margin-bottom: var(--space-4);">
              <p style="font-size: var(--text-xs); color: var(--gray-500); margin-bottom: var(--space-2);">
                Top rated:
              </p>
              <div style="display: flex; flex-direction: column; gap: var(--space-1);">
                ${topRestaurants.slice(0, 2).map(restaurant => `
                  <div style="font-size: var(--text-xs); color: var(--gray-700);">
                    ${restaurant.name} (${restaurant.rating}★)
                  </div>
                `).join('')}
              </div>
            </div>
          ` : ''}

          <div class="btn btn-outline btn-sm" style="margin-top: auto;">
            Explore
          </div>
        </div>
      </a>
    </div>
  `;
}

function loadFeaturedByCategory() {
  const categorySections = document.getElementById('category-sections');
  if (!categorySections) return;

  // Show featured restaurants for each cuisine category (limit to first 4 categories)
  const featuredCategories = categories.cuisine.slice(0, 4);

  categorySections.innerHTML = featuredCategories.map(category =>
    createCategorySection(category)
  ).join('');
}

function createCategorySection(category) {
  const categoryRestaurants = getRestaurantsByCategory(category.id)
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 4);
  
  if (categoryRestaurants.length === 0) return '';
  
  return `
    <div class="category-section" style="margin-bottom: var(--space-16);">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-8);">
        <div style="display: flex; align-items: center; gap: var(--space-4);">
          <div style="background: ${category.color}20; width: 48px; height: 48px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px;">
            ${category.icon}
          </div>
          <div>
            <h3 style="font-size: var(--text-2xl); font-weight: 600; margin-bottom: var(--space-1);">
              Best ${category.name} Restaurants
            </h3>
            <p style="color: var(--gray-600);">
              Top-rated ${category.name.toLowerCase()} cuisine in Malaysia
            </p>
          </div>
        </div>
        <a href="search.html?category=${category.id}" class="btn btn-outline">
          View All
        </a>
      </div>
      
      <div class="restaurants-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        ${categoryRestaurants.map(restaurant => createRestaurantCard(restaurant)).join('')}
      </div>
    </div>
  `;
}

function createRestaurantCard(restaurant) {
  const isFavorite = window.EatMY.favorites.includes(restaurant.id);
  
  return `
    <div class="restaurant-card" onclick="window.location.href='restaurant-detail.html?id=${restaurant.id}'" style="cursor: pointer;">
      <div style="position: relative;">
        <img src="${restaurant.image}" alt="${restaurant.name}" class="restaurant-card-image" loading="lazy">
        <button 
          class="favorite-btn" 
          data-restaurant-id="${restaurant.id}"
          onclick="event.stopPropagation(); window.EatMY.toggleFavorite(${restaurant.id})"
          style="position: absolute; top: 12px; right: 12px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 36px; height: 36px; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 16px;"
        >
          ${isFavorite ? '❤️' : '🤍'}
        </button>
        
        ${restaurant.isOpen ? 
          '<div class="status-badge open" style="position: absolute; bottom: 12px; left: 12px; background: var(--success); color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">Open Now</div>' : 
          '<div class="status-badge closed" style="position: absolute; bottom: 12px; left: 12px; background: var(--error); color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">Closed</div>'
        }
      </div>
      
      <div class="restaurant-card-content">
        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: var(--space-2);">
          <h4 style="font-size: var(--text-lg); font-weight: 600; margin: 0;">${restaurant.name}</h4>
          ${restaurant.isHalal ? '<span class="badge badge-halal">Halal</span>' : ''}
        </div>
        
        <p style="color: var(--gray-600); font-size: var(--text-sm); margin-bottom: var(--space-3);">
          ${restaurant.area} • ${restaurant.priceRange}
        </p>
        
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div class="rating">
            <div class="rating-stars">
              ${createStarRating(restaurant.rating)}
            </div>
            <span class="rating-value">${restaurant.rating}</span>
            <span class="rating-count" style="color: var(--gray-500);">(${restaurant.reviewCount})</span>
          </div>
          
          ${restaurant.badges.length > 0 ? `
            <span class="badge badge-${restaurant.badges[0]}" style="font-size: 10px;">
              ${restaurant.badges[0].replace('-', ' ')}
            </span>
          ` : ''}
        </div>
      </div>
    </div>
  `;
}

function createStarRating(rating) {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  let stars = '';
  
  for (let i = 0; i < fullStars; i++) {
    stars += '<span class="rating-star filled">★</span>';
  }
  
  if (hasHalfStar) {
    stars += '<span class="rating-star half">☆</span>';
  }
  
  for (let i = 0; i < emptyStars; i++) {
    stars += '<span class="rating-star">☆</span>';
  }
  
  return stars;
}

// Add custom styles and hover effects
document.addEventListener('DOMContentLoaded', function() {
  // Add custom styles for category tabs
  const style = document.createElement('style');
  style.textContent = `
    .category-tab {
      padding: var(--space-3) var(--space-6);
      border: none;
      background: var(--gray-100);
      color: var(--gray-700);
      border-radius: var(--radius-lg);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
      white-space: nowrap;
      font-size: var(--text-sm);
    }

    .category-tab:hover {
      background: var(--gray-200);
    }

    .category-tab.active {
      background: var(--primary-color);
      color: var(--white);
    }

    .category-tabs {
      display: flex;
      gap: var(--space-2);
      overflow-x: auto;
      padding-bottom: var(--space-2);
    }

    .category-tabs::-webkit-scrollbar {
      height: 4px;
    }

    .category-tabs::-webkit-scrollbar-track {
      background: var(--gray-100);
      border-radius: 2px;
    }

    .category-tabs::-webkit-scrollbar-thumb {
      background: var(--gray-300);
      border-radius: 2px;
    }
  `;
  document.head.appendChild(style);

  setTimeout(() => {
    const categoryCards = document.querySelectorAll('.category-card-large .card');
    categoryCards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-8px)';
        this.style.boxShadow = 'var(--shadow-xl)';
      });

      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = 'var(--shadow-sm)';
      });
    });
    
    // Add hover effects to dietary cards
    const dietaryCards = document.querySelectorAll('.dietary-card');
    dietaryCards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-4px)';
        this.style.boxShadow = 'var(--shadow-lg)';
      });
      
      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = 'var(--shadow-sm)';
      });
    });
    
    // Add hover effects to combination cards
    const combinationCards = document.querySelectorAll('.combination-card');
    combinationCards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-4px)';
        this.style.boxShadow = 'var(--shadow-lg)';
      });
      
      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = 'var(--shadow-sm)';
      });
    });
  }, 100);
});

// Initialize favorite buttons after content loads
document.addEventListener('DOMContentLoaded', function() {
  setTimeout(() => {
    const favoriteButtons = document.querySelectorAll('.favorite-btn');
    favoriteButtons.forEach(button => {
      const restaurantId = parseInt(button.dataset.restaurantId);
      const isFavorite = window.EatMY.favorites.includes(restaurantId);
      
      button.innerHTML = isFavorite ? '❤️' : '🤍';
    });
  }, 200);
});
