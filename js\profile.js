// EatMY - Profile Page Functionality

import { getReviewsByRestaurant, restaurants, reviews, getRestaurantById } from './data.js';

let currentTab = 'reviews';

// Initialize profile page
document.addEventListener('DOMContentLoaded', function() {
  initializeProfilePage();
});

function initializeProfilePage() {
  // Check if user is logged in
  if (!window.EatMY.currentUser) {
    window.location.href = 'login.html?redirect=' + encodeURIComponent(window.location.href);
    return;
  }
  
  // Load profile data
  loadProfileData();
  
  // Initialize tabs
  initializeTabs();
  
  // Initialize settings form
  initializeSettingsForm();
  
  // Load initial tab content
  loadTabContent(currentTab);
  
  console.log('Profile page initialized');
}

function loadProfileData() {
  const user = window.EatMY.currentUser;
  
  // Update profile header
  document.getElementById('profile-avatar').src = user.avatar;
  document.getElementById('profile-name').textContent = user.name;
  document.getElementById('profile-level').textContent = user.level;
  
  // Format join date
  const joinDate = new Date(user.joinDate);
  const formattedDate = joinDate.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long' 
  });
  document.getElementById('profile-join-date').textContent = `Member since ${formattedDate}`;
  
  // Update stats
  document.getElementById('profile-reviews-count').textContent = user.reviewCount;
  document.getElementById('profile-favorites-count').textContent = user.favorites.length;
  document.getElementById('profile-followers-count').textContent = user.followers;
  document.getElementById('profile-following-count').textContent = user.following;
  
  // Update page title
  document.title = `${user.name} - Profile - EatMY`;
}

function initializeTabs() {
  const tabButtons = document.querySelectorAll('.profile-tab');
  
  tabButtons.forEach(button => {
    button.addEventListener('click', function() {
      const tabName = this.dataset.tab;
      switchTab(tabName);
    });
  });
}

function switchTab(tabName) {
  // Update active tab button
  document.querySelectorAll('.profile-tab').forEach(tab => {
    tab.classList.remove('active');
  });
  document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
  
  // Update active content
  document.querySelectorAll('.profile-content').forEach(content => {
    content.style.display = 'none';
  });
  document.getElementById(`${tabName}-tab`).style.display = 'block';
  
  currentTab = tabName;
  loadTabContent(tabName);
}

function loadTabContent(tabName) {
  switch (tabName) {
    case 'reviews':
      loadUserReviews();
      break;
    case 'favorites':
      loadUserFavorites();
      break;
    case 'following':
      loadUserFollowing();
      break;
    case 'settings':
      loadUserSettings();
      break;
  }
}

function loadUserReviews() {
  const reviewsList = document.getElementById('user-reviews-list');
  const noReviews = document.getElementById('no-reviews');
  
  // Get user's reviews (in real app, this would be filtered by user ID)
  const userReviews = reviews.filter(review => review.userId === window.EatMY.currentUser.id);
  
  if (userReviews.length === 0) {
    reviewsList.style.display = 'none';
    noReviews.style.display = 'block';
    return;
  }
  
  reviewsList.style.display = 'block';
  noReviews.style.display = 'none';
  
  reviewsList.innerHTML = userReviews.map(review => {
    const restaurant = getRestaurantById(review.restaurantId);
    return createUserReviewHTML(review, restaurant);
  }).join('');
}

function createUserReviewHTML(review, restaurant) {
  return `
    <div class="user-review-item" style="background: var(--white); border-radius: var(--radius-xl); padding: var(--space-6); box-shadow: var(--shadow-sm); margin-bottom: var(--space-6);">
      <div style="display: flex; gap: var(--space-4); margin-bottom: var(--space-4);">
        <img 
          src="${restaurant.image}" 
          alt="${restaurant.name}"
          style="width: 80px; height: 80px; object-fit: cover; border-radius: var(--radius-lg); cursor: pointer;"
          onclick="window.location.href='restaurant-detail.html?id=${restaurant.id}'"
        >
        <div style="flex: 1;">
          <h3 style="margin-bottom: var(--space-2);">
            <a href="restaurant-detail.html?id=${restaurant.id}" style="color: var(--gray-900); text-decoration: none;">
              ${restaurant.name}
            </a>
          </h3>
          <p style="color: var(--gray-600); font-size: var(--text-sm); margin-bottom: var(--space-2);">
            ${getCuisineDisplayName(restaurant.cuisine)} • ${restaurant.area}
          </p>
          <div style="display: flex; align-items: center; gap: var(--space-4);">
            <div class="rating">
              <div class="rating-stars">
                ${createStarRating(review.rating)}
              </div>
              <span class="rating-value">${review.rating}</span>
            </div>
            <span style="color: var(--gray-500); font-size: var(--text-sm);">
              ${formatDate(review.date)}
            </span>
          </div>
        </div>
      </div>
      
      ${review.title ? `<h4 style="margin-bottom: var(--space-2); font-weight: 600;">${review.title}</h4>` : ''}
      <p style="color: var(--gray-700); line-height: 1.6; margin-bottom: var(--space-4);">
        ${review.content}
      </p>
      
      ${review.images && review.images.length > 0 ? `
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: var(--space-2); margin-bottom: var(--space-4);">
          ${review.images.map(image => `
            <img src="${image}" alt="Review photo" style="width: 100%; height: 100px; object-fit: cover; border-radius: var(--radius-md); cursor: pointer;">
          `).join('')}
        </div>
      ` : ''}
      
      <div style="display: flex; gap: var(--space-4); align-items: center; font-size: var(--text-sm); color: var(--gray-600);">
        <span>👍 ${review.helpful} helpful</span>
        <span>💬 ${review.replies ? review.replies.length : 0} replies</span>
        <button class="btn btn-outline btn-sm" onclick="editReview(${review.id})">Edit</button>
        <button class="btn btn-outline btn-sm" onclick="deleteReview(${review.id})" style="color: var(--error); border-color: var(--error);">Delete</button>
      </div>
    </div>
  `;
}

function loadUserFavorites() {
  const favoritesList = document.getElementById('user-favorites-list');
  const noFavorites = document.getElementById('no-favorites');
  
  const userFavorites = window.EatMY.currentUser.favorites;
  
  if (userFavorites.length === 0) {
    favoritesList.style.display = 'none';
    noFavorites.style.display = 'block';
    return;
  }
  
  favoritesList.style.display = 'grid';
  noFavorites.style.display = 'none';
  
  const favoriteRestaurants = userFavorites.map(id => getRestaurantById(id)).filter(Boolean);
  
  favoritesList.innerHTML = favoriteRestaurants.map(restaurant => 
    createFavoriteRestaurantCard(restaurant)
  ).join('');
}

function createFavoriteRestaurantCard(restaurant) {
  return `
    <div class="restaurant-card" onclick="window.location.href='restaurant-detail.html?id=${restaurant.id}'" style="cursor: pointer;">
      <div style="position: relative;">
        <img src="${restaurant.image}" alt="${restaurant.name}" class="restaurant-card-image">
        <button 
          class="favorite-btn" 
          data-restaurant-id="${restaurant.id}"
          onclick="event.stopPropagation(); window.EatMY.toggleFavorite(${restaurant.id}); setTimeout(() => loadUserFavorites(), 100);"
          style="position: absolute; top: 12px; right: 12px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 36px; height: 36px; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 16px;"
        >
          ❤️
        </button>
      </div>
      
      <div class="restaurant-card-content">
        <h3 class="restaurant-card-title">${restaurant.name}</h3>
        <p class="restaurant-card-cuisine">${getCuisineDisplayName(restaurant.cuisine)} • ${restaurant.area}</p>
        
        <div class="restaurant-card-footer">
          <div class="rating">
            <div class="rating-stars">
              ${createStarRating(restaurant.rating)}
            </div>
            <span class="rating-value">${restaurant.rating}</span>
            <span class="rating-count">(${restaurant.reviewCount})</span>
          </div>
          
          <div class="price-range text-sm text-gray-600">
            ${restaurant.priceRange}
          </div>
        </div>
      </div>
    </div>
  `;
}

function loadUserFollowing() {
  const followingList = document.getElementById('user-following-list');
  const noFollowing = document.getElementById('no-following');
  
  // For demo purposes, show empty state
  followingList.style.display = 'none';
  noFollowing.style.display = 'block';
}

function loadUserSettings() {
  const user = window.EatMY.currentUser;
  
  // Populate form with current user data
  document.getElementById('settings-name').value = user.name;
  document.getElementById('settings-email').value = user.email;
  document.getElementById('settings-language').value = window.EatMY.currentLanguage;
}

function initializeSettingsForm() {
  const settingsForm = document.getElementById('profile-settings-form');
  
  if (settingsForm) {
    settingsForm.addEventListener('submit', handleSettingsUpdate);
  }
}

function handleSettingsUpdate(e) {
  e.preventDefault();
  
  const formData = new FormData(e.target);
  const name = formData.get('name');
  const email = formData.get('email');
  const bio = formData.get('bio');
  const language = formData.get('language');
  const currentPassword = formData.get('current_password');
  const newPassword = formData.get('new_password');
  const confirmPassword = formData.get('confirm_password');
  
  // Validate password change if provided
  if (newPassword) {
    if (newPassword !== confirmPassword) {
      window.EatMY.showNotification('New passwords do not match', 'error');
      return;
    }
    
    if (!currentPassword) {
      window.EatMY.showNotification('Current password is required to change password', 'error');
      return;
    }
  }
  
  // Show loading state
  const submitBtn = e.target.querySelector('button[type="submit"]');
  const originalText = submitBtn.textContent;
  submitBtn.textContent = 'Saving...';
  submitBtn.disabled = true;
  
  // Simulate API call
  setTimeout(() => {
    // Update user data
    const updatedUser = {
      ...window.EatMY.currentUser,
      name: name,
      email: email,
      bio: bio
    };
    
    // Save updated user
    window.EatMY.currentUser = updatedUser;
    localStorage.setItem('eatmy_user', JSON.stringify(updatedUser));
    
    // Update language if changed
    if (language !== window.EatMY.currentLanguage) {
      window.EatMY.currentLanguage = language;
      localStorage.setItem('eatmy_language', language);
    }
    
    // Update profile display
    loadProfileData();
    
    // Reset form state
    submitBtn.textContent = originalText;
    submitBtn.disabled = false;
    
    // Clear password fields
    document.getElementById('settings-current-password').value = '';
    document.getElementById('settings-new-password').value = '';
    document.getElementById('settings-confirm-password').value = '';
    
    window.EatMY.showNotification('Settings updated successfully!', 'success');
    
  }, 1000);
}

// Utility functions
function createStarRating(rating) {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  let stars = '';
  
  for (let i = 0; i < fullStars; i++) {
    stars += '<span class="rating-star filled">★</span>';
  }
  
  if (hasHalfStar) {
    stars += '<span class="rating-star half">☆</span>';
  }
  
  for (let i = 0; i < emptyStars; i++) {
    stars += '<span class="rating-star">☆</span>';
  }
  
  return stars;
}

function getCuisineDisplayName(cuisine) {
  const cuisineMap = {
    'malay': 'Malay',
    'chinese': 'Chinese',
    'indian': 'Indian',
    'western': 'Western',
    'japanese': 'Japanese',
    'korean': 'Korean',
    'thai': 'Thai',
    'fusion': 'Fusion'
  };
  
  return cuisineMap[cuisine] || cuisine;
}

function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
}

function resetForm() {
  loadUserSettings();
  window.EatMY.showNotification('Changes discarded', 'info');
}

function editReview(reviewId) {
  window.location.href = `review.html?edit=${reviewId}`;
}

function deleteReview(reviewId) {
  if (confirm('Are you sure you want to delete this review?')) {
    // In a real app, this would make an API call
    window.EatMY.showNotification('Review deleted successfully', 'success');
    
    // Reload reviews
    setTimeout(() => {
      loadUserReviews();
    }, 500);
  }
}

// Make functions available globally
window.resetForm = resetForm;
window.editReview = editReview;
window.deleteReview = deleteReview;
