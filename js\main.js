// EatMY - Main Application Logic

import { translations } from './data.js';

// Global state
window.EatMY = {
  currentUser: null,
  currentLanguage: 'en',
  favorites: JSON.parse(localStorage.getItem('eatmy_favorites') || '[]'),
  searchHistory: JSON.parse(localStorage.getItem('eatmy_search_history') || '[]')
};

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
  initializeApp();
});

function initializeApp() {
  // Load user session
  loadUserSession();
  
  // Initialize language
  initializeLanguage();
  
  // Initialize navigation
  initializeNavigation();
  
  // Initialize search functionality
  initializeSearch();
  
  // Initialize modals
  initializeModals();
  
  // Initialize page-specific functionality
  initializePageFunctionality();
  
  console.log('EatMY App initialized successfully');
}

// User Session Management
function loadUserSession() {
  const savedUser = localStorage.getItem('eatmy_user');
  if (savedUser) {
    window.EatMY.currentUser = JSON.parse(savedUser);
    updateUserInterface();
  }
}

function saveUserSession(user) {
  window.EatMY.currentUser = user;
  localStorage.setItem('eatmy_user', JSON.stringify(user));
  updateUserInterface();
}

function logout() {
  window.EatMY.currentUser = null;
  localStorage.removeItem('eatmy_user');
  updateUserInterface();
  window.location.href = 'index.html';
}

function updateUserInterface() {
  const loginBtn = document.querySelector('.login-btn');
  const userMenu = document.querySelector('.user-menu');
  const profileLink = document.querySelector('.profile-link');
  
  if (window.EatMY.currentUser) {
    if (loginBtn) loginBtn.style.display = 'none';
    if (userMenu) userMenu.style.display = 'block';
    if (profileLink) {
      profileLink.textContent = window.EatMY.currentUser.name;
      profileLink.href = 'profile.html';
    }
  } else {
    if (loginBtn) loginBtn.style.display = 'block';
    if (userMenu) userMenu.style.display = 'none';
  }
}

// Language Management
function initializeLanguage() {
  const savedLanguage = localStorage.getItem('eatmy_language');
  if (savedLanguage) {
    window.EatMY.currentLanguage = savedLanguage;
  }
  
  // Initialize language switcher
  const languageButton = document.querySelector('.language-button');
  const languageDropdown = document.querySelector('.language-dropdown');
  const languageOptions = document.querySelectorAll('.language-option');
  
  if (languageButton && languageDropdown) {
    languageButton.addEventListener('click', function(e) {
      e.stopPropagation();
      languageDropdown.classList.toggle('active');
    });
    
    document.addEventListener('click', function() {
      languageDropdown.classList.remove('active');
    });
  }
  
  if (languageOptions.length > 0) {
    languageOptions.forEach(option => {
      option.addEventListener('click', function() {
        const language = this.dataset.language;
        changeLanguage(language);
      });
    });
  }
  
  updateLanguageInterface();
}

function changeLanguage(language) {
  window.EatMY.currentLanguage = language;
  localStorage.setItem('eatmy_language', language);
  updateLanguageInterface();
}

function updateLanguageInterface() {
  const elements = document.querySelectorAll('[data-translate]');
  elements.forEach(element => {
    const key = element.dataset.translate;
    const translation = translations[window.EatMY.currentLanguage];
    if (translation && translation[key]) {
      if (element.tagName === 'INPUT' && element.type === 'text') {
        element.placeholder = translation[key];
      } else {
        element.textContent = translation[key];
      }
    }
  });
  
  // Update language button text
  const languageButton = document.querySelector('.language-button span');
  if (languageButton) {
    const languageNames = { en: 'EN', zh: '中文', ms: 'MS' };
    languageButton.textContent = languageNames[window.EatMY.currentLanguage];
  }
}

// Navigation
function initializeNavigation() {
  const navToggle = document.querySelector('.nav-toggle');
  const navLinks = document.querySelector('.nav-links');
  
  if (navToggle && navLinks) {
    navToggle.addEventListener('click', function() {
      navLinks.classList.toggle('active');
    });
    
    // Close mobile menu when clicking on links
    const navLinkItems = navLinks.querySelectorAll('a');
    navLinkItems.forEach(link => {
      link.addEventListener('click', function() {
        navLinks.classList.remove('active');
      });
    });
  }
  
  // Highlight active page
  highlightActivePage();
}

function highlightActivePage() {
  const currentPage = window.location.pathname.split('/').pop() || 'index.html';
  const navLinks = document.querySelectorAll('.nav-link');
  
  navLinks.forEach(link => {
    const href = link.getAttribute('href');
    if (href === currentPage || (currentPage === '' && href === 'index.html')) {
      link.classList.add('active');
    }
  });
}

// Search Functionality
function initializeSearch() {
  const searchForms = document.querySelectorAll('.search-form');
  const searchInputs = document.querySelectorAll('.search-input');
  
  searchForms.forEach(form => {
    form.addEventListener('submit', function(e) {
      e.preventDefault();
      const query = form.querySelector('.search-input').value.trim();
      if (query) {
        performSearch(query);
      }
    });
  });
  
  // Search suggestions
  searchInputs.forEach(input => {
    input.addEventListener('input', function() {
      const query = this.value.trim();
      if (query.length > 2) {
        showSearchSuggestions(query, this);
      } else {
        hideSearchSuggestions();
      }
    });
    
    input.addEventListener('blur', function() {
      // Delay hiding suggestions to allow clicking
      setTimeout(hideSearchSuggestions, 200);
    });
  });
}

function performSearch(query) {
  // Save to search history
  if (!window.EatMY.searchHistory.includes(query)) {
    window.EatMY.searchHistory.unshift(query);
    window.EatMY.searchHistory = window.EatMY.searchHistory.slice(0, 10);
    localStorage.setItem('eatmy_search_history', JSON.stringify(window.EatMY.searchHistory));
  }
  
  // Redirect to search page with query
  const searchUrl = `search.html?q=${encodeURIComponent(query)}`;
  window.location.href = searchUrl;
}

function showSearchSuggestions(query, input) {
  // This would typically fetch suggestions from an API
  // For now, we'll show search history
  const suggestions = window.EatMY.searchHistory.filter(item => 
    item.toLowerCase().includes(query.toLowerCase())
  ).slice(0, 5);
  
  if (suggestions.length > 0) {
    createSuggestionDropdown(suggestions, input);
  }
}

function createSuggestionDropdown(suggestions, input) {
  // Remove existing dropdown
  hideSearchSuggestions();
  
  const dropdown = document.createElement('div');
  dropdown.className = 'search-suggestions';
  dropdown.style.cssText = `
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 100;
    max-height: 200px;
    overflow-y: auto;
  `;
  
  suggestions.forEach(suggestion => {
    const item = document.createElement('div');
    item.className = 'suggestion-item';
    item.textContent = suggestion;
    item.style.cssText = `
      padding: var(--space-3);
      cursor: pointer;
      border-bottom: 1px solid var(--gray-100);
      transition: background var(--transition-fast);
    `;
    
    item.addEventListener('mouseenter', function() {
      this.style.background = 'var(--gray-50)';
    });
    
    item.addEventListener('mouseleave', function() {
      this.style.background = 'transparent';
    });
    
    item.addEventListener('click', function() {
      input.value = suggestion;
      performSearch(suggestion);
    });
    
    dropdown.appendChild(item);
  });
  
  // Position dropdown relative to input
  const searchContainer = input.closest('.search-bar') || input.parentElement;
  searchContainer.style.position = 'relative';
  searchContainer.appendChild(dropdown);
}

function hideSearchSuggestions() {
  const existingDropdown = document.querySelector('.search-suggestions');
  if (existingDropdown) {
    existingDropdown.remove();
  }
}

// Modal Management
function initializeModals() {
  const modalTriggers = document.querySelectorAll('[data-modal]');
  const modalCloses = document.querySelectorAll('.modal-close, .modal-overlay');
  
  modalTriggers.forEach(trigger => {
    trigger.addEventListener('click', function(e) {
      e.preventDefault();
      const modalId = this.dataset.modal;
      openModal(modalId);
    });
  });
  
  modalCloses.forEach(close => {
    close.addEventListener('click', function(e) {
      if (e.target === this) {
        closeModal();
      }
    });
  });
  
  // Close modal with Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeModal();
    }
  });
}

function openModal(modalId) {
  const modal = document.getElementById(modalId);
  if (modal) {
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
  }
}

function closeModal() {
  const activeModal = document.querySelector('.modal-overlay.active');
  if (activeModal) {
    activeModal.classList.remove('active');
    document.body.style.overflow = '';
  }
}

// Favorites Management
function toggleFavorite(restaurantId) {
  const index = window.EatMY.favorites.indexOf(restaurantId);
  
  if (index > -1) {
    window.EatMY.favorites.splice(index, 1);
  } else {
    window.EatMY.favorites.push(restaurantId);
  }
  
  localStorage.setItem('eatmy_favorites', JSON.stringify(window.EatMY.favorites));
  updateFavoriteButtons();
  
  // Show feedback
  showNotification(
    index > -1 ? 'Removed from favorites' : 'Added to favorites',
    'success'
  );
}

function updateFavoriteButtons() {
  const favoriteButtons = document.querySelectorAll('.favorite-btn');
  favoriteButtons.forEach(btn => {
    const restaurantId = parseInt(btn.dataset.restaurantId);
    const isFavorite = window.EatMY.favorites.includes(restaurantId);
    
    btn.classList.toggle('active', isFavorite);
    btn.innerHTML = isFavorite ? '❤️' : '🤍';
  });
}

// Notifications
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--${type === 'success' ? 'success' : type === 'error' ? 'error' : 'info'});
    color: white;
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: 2000;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
  `;
  
  document.body.appendChild(notification);
  
  // Animate in
  setTimeout(() => {
    notification.style.transform = 'translateX(0)';
  }, 100);
  
  // Remove after 3 seconds
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 300);
  }, 3000);
}

// Page-specific initialization
function initializePageFunctionality() {
  const currentPage = window.location.pathname.split('/').pop() || 'index.html';
  
  switch (currentPage) {
    case 'index.html':
    case '':
      initializeHomePage();
      break;
    case 'search.html':
      initializeSearchPage();
      break;
    case 'restaurant-detail.html':
      initializeRestaurantDetailPage();
      break;
    case 'login.html':
      initializeLoginPage();
      break;
    case 'profile.html':
      initializeProfilePage();
      break;
  }
}

function initializeHomePage() {
  // Initialize featured restaurants carousel
  initializeCarousel();
  
  // Initialize category quick access
  initializeCategoryCards();
}

function initializeSearchPage() {
  // Parse URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const query = urlParams.get('q');
  
  if (query) {
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
      searchInput.value = query;
    }
    // Perform search with query
    // This would be implemented in search.js
  }
}

function initializeRestaurantDetailPage() {
  // Initialize image gallery
  initializeImageGallery();
  
  // Initialize review system
  initializeReviewSystem();
}

function initializeLoginPage() {
  // Initialize login/register forms
  initializeAuthForms();
}

function initializeProfilePage() {
  // Check if user is logged in
  if (!window.EatMY.currentUser) {
    window.location.href = 'login.html';
    return;
  }
  
  // Initialize profile tabs
  initializeProfileTabs();
}

// Utility functions
function initializeCarousel() {
  // Carousel functionality would be implemented here
  console.log('Carousel initialized');
}

function initializeCategoryCards() {
  // Category card interactions would be implemented here
  console.log('Category cards initialized');
}

function initializeImageGallery() {
  // Image gallery functionality would be implemented here
  console.log('Image gallery initialized');
}

function initializeReviewSystem() {
  // Review system functionality would be implemented here
  console.log('Review system initialized');
}

function initializeAuthForms() {
  // Authentication form handling would be implemented here
  console.log('Auth forms initialized');
}

function initializeProfileTabs() {
  // Profile tab functionality would be implemented here
  console.log('Profile tabs initialized');
}

// Export functions for use in other modules
window.EatMY.toggleFavorite = toggleFavorite;
window.EatMY.showNotification = showNotification;
window.EatMY.openModal = openModal;
window.EatMY.closeModal = closeModal;
window.EatMY.logout = logout;
