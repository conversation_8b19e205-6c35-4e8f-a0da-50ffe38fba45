<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EatMY - Discover Amazing Food in Malaysia</title>
    <meta name="description" content="Discover the best restaurants and food experiences in Malaysia. Read reviews, explore cuisines, and find your next favorite meal.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/icons/favicon.ico">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="EatMY - Discover Amazing Food in Malaysia">
    <meta property="og:description" content="Malaysia's premier food review and restaurant discovery platform">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://eatmy.com">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="index.html" class="nav-brand">EatMY</a>
            
            <ul class="nav-links">
                <li><a href="index.html" class="nav-link" data-translate="home">Home</a></li>
                <li><a href="search.html" class="nav-link" data-translate="search">Search</a></li>
                <li><a href="rankings.html" class="nav-link" data-translate="rankings">Rankings</a></li>
                <li><a href="categories.html" class="nav-link" data-translate="categories">Categories</a></li>
                <li><a href="map.html" class="nav-link" data-translate="map">Map</a></li>
            </ul>
            
            <div class="nav-actions">
                <!-- Language Switcher -->
                <div class="language-switcher">
                    <button class="language-button">
                        <span>EN</span>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                    </button>
                    <div class="language-dropdown">
                        <button class="language-option" data-language="en">English</button>
                        <button class="language-option" data-language="zh">中文</button>
                        <button class="language-option" data-language="ms">Bahasa</button>
                    </div>
                </div>
                
                <!-- User Menu -->
                <div class="user-menu" style="display: none;">
                    <a href="profile.html" class="profile-link btn btn-outline">Profile</a>
                    <button onclick="EatMY.logout()" class="btn btn-secondary" data-translate="logout">Logout</button>
                </div>
                
                <!-- Login Button -->
                <a href="login.html" class="login-btn btn btn-primary" data-translate="login">Login</a>
            </div>
            
            <button class="nav-toggle">☰</button>
        </nav>
    </header>

    <!-- Main Content -->
    <main style="margin-top: var(--header-height);">
        <!-- Hero Section -->
        <section class="hero section">
            <div class="container">
                <div class="hero-content text-center">
                    <h1 class="hero-title">Discover Amazing Food in Malaysia</h1>
                    <p class="hero-subtitle text-lg text-gray-600 mb-8">
                        Find the best restaurants, read authentic reviews, and explore Malaysia's incredible food scene
                    </p>
                    
                    <!-- Search Bar -->
                    <div class="search-bar" style="margin: 0 auto;">
                        <form class="search-form">
                            <input 
                                type="text" 
                                class="search-input" 
                                placeholder="Search restaurants, cuisines..."
                                data-translate="searchPlaceholder"
                            >
                            <button type="submit" class="search-button">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"></circle>
                                    <path d="m21 21-4.35-4.35"></path>
                                </svg>
                            </button>
                        </form>
                    </div>
                    
                    <!-- Quick Categories -->
                    <div class="quick-categories mt-8">
                        <div class="grid grid-cols-4 md:grid-cols-8 gap-4">
                            <a href="search.html?category=malay" class="category-quick">
                                <div class="category-icon">🍛</div>
                                <span class="category-name">Malay</span>
                            </a>
                            <a href="search.html?category=chinese" class="category-quick">
                                <div class="category-icon">🥢</div>
                                <span class="category-name">Chinese</span>
                            </a>
                            <a href="search.html?category=indian" class="category-quick">
                                <div class="category-icon">🍛</div>
                                <span class="category-name">Indian</span>
                            </a>
                            <a href="search.html?category=western" class="category-quick">
                                <div class="category-icon">🍔</div>
                                <span class="category-name">Western</span>
                            </a>
                            <a href="search.html?category=japanese" class="category-quick">
                                <div class="category-icon">🍣</div>
                                <span class="category-name">Japanese</span>
                            </a>
                            <a href="search.html?category=korean" class="category-quick">
                                <div class="category-icon">🍜</div>
                                <span class="category-name">Korean</span>
                            </a>
                            <a href="search.html?category=thai" class="category-quick">
                                <div class="category-icon">🌶️</div>
                                <span class="category-name">Thai</span>
                            </a>
                            <a href="categories.html" class="category-quick">
                                <div class="category-icon">➕</div>
                                <span class="category-name" data-translate="viewAll">More</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Restaurants -->
        <section class="featured-restaurants section-sm">
            <div class="container">
                <div class="section-header flex justify-between items-center mb-8">
                    <h2 data-translate="featuredRestaurants">Featured Restaurants</h2>
                    <a href="restaurants.html" class="btn btn-outline" data-translate="viewAll">View All</a>
                </div>
                
                <div class="restaurants-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Restaurant cards will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Top Rated Section -->
        <section class="top-rated section-sm" style="background: var(--white);">
            <div class="container">
                <div class="section-header flex justify-between items-center mb-8">
                    <h2 data-translate="topRated">Top Rated This Week</h2>
                    <a href="rankings.html" class="btn btn-outline" data-translate="viewAll">View All</a>
                </div>
                
                <div class="top-rated-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Top rated restaurants will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Categories Section -->
        <section class="categories-section section-sm">
            <div class="container">
                <div class="section-header text-center mb-8">
                    <h2 data-translate="categories">Explore by Categories</h2>
                    <p class="text-gray-600">Discover restaurants by your favorite cuisine type</p>
                </div>
                
                <div class="categories-grid grid grid-cols-2 md:grid-cols-4 gap-6">
                    <!-- Categories will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Popular Areas -->
        <section class="popular-areas section-sm" style="background: var(--white);">
            <div class="container">
                <div class="section-header text-center mb-8">
                    <h2>Popular Areas</h2>
                    <p class="text-gray-600">Find great food in these popular locations</p>
                </div>
                
                <div class="areas-grid grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    <a href="search.html?area=Kuala Lumpur" class="area-card">
                        <div class="area-name font-semibold">Kuala Lumpur</div>
                        <div class="area-count text-sm text-gray-500">1,234 restaurants</div>
                    </a>
                    <a href="search.html?area=Penang" class="area-card">
                        <div class="area-name font-semibold">Penang</div>
                        <div class="area-count text-sm text-gray-500">856 restaurants</div>
                    </a>
                    <a href="search.html?area=Johor" class="area-card">
                        <div class="area-name font-semibold">Johor</div>
                        <div class="area-count text-sm text-gray-500">642 restaurants</div>
                    </a>
                    <a href="search.html?area=Selangor" class="area-card">
                        <div class="area-name font-semibold">Selangor</div>
                        <div class="area-count text-sm text-gray-500">987 restaurants</div>
                    </a>
                    <a href="search.html?area=Perak" class="area-card">
                        <div class="area-name font-semibold">Perak</div>
                        <div class="area-count text-sm text-gray-500">423 restaurants</div>
                    </a>
                    <a href="search.html?area=Kedah" class="area-card">
                        <div class="area-name font-semibold">Kedah</div>
                        <div class="area-count text-sm text-gray-500">312 restaurants</div>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer" style="background: var(--gray-900); color: var(--white); padding: var(--space-16) 0;">
        <div class="container">
            <div class="footer-content grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="footer-section">
                    <h3 class="footer-title">EatMY</h3>
                    <p class="footer-description">Malaysia's premier food review and restaurant discovery platform</p>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-heading">Explore</h4>
                    <ul class="footer-links">
                        <li><a href="restaurants.html">All Restaurants</a></li>
                        <li><a href="categories.html">Categories</a></li>
                        <li><a href="rankings.html">Top Rankings</a></li>
                        <li><a href="map.html">Map View</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-heading">Community</h4>
                    <ul class="footer-links">
                        <li><a href="review.html">Write a Review</a></li>
                        <li><a href="profile.html">My Profile</a></li>
                        <li><a href="help.html">Help Center</a></li>
                        <li><a href="about.html">About Us</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-heading">Connect</h4>
                    <div class="social-links">
                        <a href="#" class="social-link">Facebook</a>
                        <a href="#" class="social-link">Instagram</a>
                        <a href="#" class="social-link">Twitter</a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom" style="border-top: 1px solid var(--gray-700); margin-top: var(--space-8); padding-top: var(--space-8); text-center;">
                <p>&copy; 2024 EatMY. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <!-- 加载数据文件（必须先加载） -->
    <script src="js/data.js"></script>
    <!-- 加载应用逻辑 -->
    <script src="js/main.js"></script>
    <script>
        // 确保数据加载完成后再初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 等待一小段时间确保所有脚本都加载完成
            setTimeout(function() {
                if (window.restaurants && window.restaurants.length > 0) {
                    console.log('Data loaded successfully, initializing page...');
                    // 重新触发页面初始化
                    if (window.EatMYHome) {
                        window.EatMYHome.loadFeaturedRestaurants();
                        window.EatMYHome.loadTopRatedRestaurants();
                    }
                } else {
                    console.error('Restaurant data still not available');
                }
            }, 100);
        });
    </script>
    <script src="js/home.js"></script>
</body>
</html>
