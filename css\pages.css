/* EatMY - Page-specific Styles */

/* Search Page */
.search-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: var(--space-8);
}

@media (max-width: 1024px) {
  .search-layout {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
  
  .filters-sidebar {
    order: 2;
  }
  
  .search-results {
    order: 1;
  }
}

/* Filters Sidebar */
.filters-sidebar {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
  height: fit-content;
  position: sticky;
  top: calc(var(--header-height) + var(--space-4));
}

.filters-title {
  font-size: var(--text-xl);
  font-weight: 600;
  margin-bottom: var(--space-6);
  color: var(--gray-900);
}

.filter-group {
  margin-bottom: var(--space-6);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
}

.filter-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.filter-title {
  font-size: var(--text-base);
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: var(--gray-800);
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.filter-option {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: background var(--transition-fast);
}

.filter-option:hover {
  background: var(--gray-50);
}

.filter-option input[type="radio"],
.filter-option input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.filter-label {
  font-size: var(--text-sm);
  color: var(--gray-700);
  cursor: pointer;
}

/* Results Area */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
  flex-wrap: wrap;
  gap: var(--space-4);
}

.results-title {
  font-size: var(--text-2xl);
  font-weight: 600;
  margin-bottom: var(--space-2);
  color: var(--gray-900);
}

.results-count {
  color: var(--gray-600);
  margin: 0;
}

.results-controls {
  display: flex;
  gap: var(--space-4);
  align-items: center;
}

.view-toggle {
  display: flex;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.view-btn {
  padding: var(--space-2) var(--space-3);
  border: none;
  background: white;
  color: var(--gray-600);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.view-btn.active,
.view-btn:hover {
  background: var(--primary-color);
  color: white;
}

/* Results List View */
.results-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.restaurant-list-item:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  gap: var(--space-2);
  margin-top: var(--space-8);
}

.pagination .btn {
  min-width: 40px;
}

/* Restaurant Detail Page */
.restaurant-hero {
  position: relative;
  height: 400px;
  overflow: hidden;
  border-radius: var(--radius-xl);
  margin-bottom: var(--space-8);
}

.restaurant-hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.restaurant-hero-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: var(--space-8);
}

.restaurant-hero-title {
  font-size: var(--text-4xl);
  font-weight: 700;
  margin-bottom: var(--space-2);
  color: white;
}

.restaurant-hero-subtitle {
  font-size: var(--text-lg);
  opacity: 0.9;
  margin-bottom: var(--space-4);
}

.restaurant-hero-actions {
  display: flex;
  gap: var(--space-4);
  align-items: center;
}

.restaurant-info-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--space-8);
  margin-bottom: var(--space-8);
}

@media (max-width: 768px) {
  .restaurant-info-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }
}

.restaurant-main-info {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-sm);
}

.restaurant-sidebar {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.info-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-sm);
}

.info-card-title {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

/* Rating Breakdown */
.rating-breakdown {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.rating-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rating-label {
  font-weight: 500;
  color: var(--gray-700);
}

.rating-bar {
  flex: 1;
  height: 8px;
  background: var(--gray-200);
  border-radius: 4px;
  margin: 0 var(--space-3);
  overflow: hidden;
}

.rating-fill {
  height: 100%;
  background: var(--accent-color);
  border-radius: 4px;
  transition: width var(--transition-normal);
}

.rating-score {
  font-weight: 600;
  color: var(--gray-800);
  min-width: 30px;
  text-align: right;
}

/* Opening Hours */
.hours-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.hours-item {
  display: flex;
  justify-content: space-between;
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--gray-100);
}

.hours-item:last-child {
  border-bottom: none;
}

.hours-day {
  font-weight: 500;
  color: var(--gray-700);
}

.hours-time {
  color: var(--gray-600);
}

.hours-time.closed {
  color: var(--error);
}

.hours-time.current {
  color: var(--success);
  font-weight: 600;
}

/* Reviews Section */
.reviews-section {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-sm);
}

.reviews-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-6);
}

.reviews-title {
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--gray-900);
}

.review-item {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.review-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.review-user-info {
  flex: 1;
}

.review-user-name {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--space-1);
}

.review-date {
  font-size: var(--text-sm);
  color: var(--gray-500);
}

.review-rating {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.review-content {
  margin-bottom: var(--space-4);
}

.review-title {
  font-weight: 600;
  margin-bottom: var(--space-2);
  color: var(--gray-900);
}

.review-text {
  color: var(--gray-700);
  line-height: 1.6;
}

.review-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.review-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: transform var(--transition-fast);
}

.review-image:hover {
  transform: scale(1.05);
}

.review-actions {
  display: flex;
  gap: var(--space-4);
  align-items: center;
}

.review-action {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  background: none;
  border: none;
  color: var(--gray-600);
  cursor: pointer;
  font-size: var(--text-sm);
  transition: color var(--transition-fast);
}

.review-action:hover {
  color: var(--primary-color);
}

.review-action.active {
  color: var(--primary-color);
}

/* Login Page */
.auth-container {
  min-height: calc(100vh - var(--header-height));
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) 0;
}

.auth-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 400px;
}

.auth-title {
  text-align: center;
  font-size: var(--text-3xl);
  font-weight: 700;
  margin-bottom: var(--space-2);
  color: var(--gray-900);
}

.auth-subtitle {
  text-align: center;
  color: var(--gray-600);
  margin-bottom: var(--space-8);
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.auth-divider {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin: var(--space-6) 0;
}

.auth-divider::before,
.auth-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: var(--gray-300);
}

.auth-divider span {
  color: var(--gray-500);
  font-size: var(--text-sm);
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  background: var(--white);
  color: var(--gray-700);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.social-btn:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
  color: var(--gray-800);
}

.auth-footer {
  text-align: center;
  margin-top: var(--space-6);
  color: var(--gray-600);
}

.auth-footer a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.auth-footer a:hover {
  text-decoration: underline;
}

/* Profile Page */
.profile-header {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--space-8);
}

.profile-info {
  display: flex;
  align-items: center;
  gap: var(--space-6);
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--primary-color);
}

.profile-details h1 {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin-bottom: var(--space-2);
}

.profile-stats {
  display: flex;
  gap: var(--space-6);
  margin-top: var(--space-4);
}

.profile-stat {
  text-align: center;
}

.profile-stat-value {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--primary-color);
}

.profile-stat-label {
  font-size: var(--text-sm);
  color: var(--gray-600);
}

.profile-tabs {
  display: flex;
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--space-2);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--space-8);
}

.profile-tab {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  border: none;
  background: none;
  border-radius: var(--radius-lg);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.profile-tab.active {
  background: var(--primary-color);
  color: var(--white);
}

.profile-tab:not(.active):hover {
  background: var(--gray-50);
}

.profile-content {
  background: var(--white);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-layout {
    grid-template-columns: 1fr;
  }
  
  .filters-sidebar {
    position: static;
    order: 2;
  }
  
  .restaurant-info-grid {
    grid-template-columns: 1fr;
  }
  
  .restaurant-hero {
    height: 250px;
  }
  
  .restaurant-hero-title {
    font-size: var(--text-2xl);
  }
  
  .results-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .results-controls {
    justify-content: space-between;
  }
  
  .profile-info {
    flex-direction: column;
    text-align: center;
  }
  
  .profile-stats {
    justify-content: center;
  }
}
