#!/bin/bash

echo "========================================"
echo "   EatMY - Local Development Server"
echo "========================================"
echo ""
echo "Starting local HTTP server..."
echo ""
echo "The website will be available at:"
echo "http://localhost:8000"
echo ""
echo "Press Ctrl+C to stop the server"
echo "========================================"
echo ""

# Try Python 3 first
if command -v python3 &> /dev/null; then
    python3 -m http.server 8000
elif command -v python &> /dev/null; then
    python -m http.server 8000 2>/dev/null || python -m SimpleHTTPServer 8000
else
    echo "Error: Python is not installed"
    echo ""
    echo "Please install Python from https://python.org"
    echo "Or use any other HTTP server like:"
    echo "- Node.js: npx http-server"
    echo "- PHP: php -S localhost:8000"
    echo ""
fi
