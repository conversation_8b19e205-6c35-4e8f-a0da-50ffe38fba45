<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EatMY Demo - Enhanced Features</title>
    <meta name="description" content="Explore the enhanced features of EatMY - Malaysia's premier food review platform.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="index.html" class="nav-brand">EatMY</a>
            
            <ul class="nav-links">
                <li><a href="index.html" class="nav-link" data-translate="home">Home</a></li>
                <li><a href="search.html" class="nav-link" data-translate="search">Search</a></li>
                <li><a href="rankings.html" class="nav-link" data-translate="rankings">Rankings</a></li>
                <li><a href="categories.html" class="nav-link" data-translate="categories">Categories</a></li>
                <li><a href="map.html" class="nav-link" data-translate="map">Map</a></li>
            </ul>
            
            <div class="nav-actions">
                <!-- Language Switcher -->
                <div class="language-switcher">
                    <button class="language-button">
                        <span>EN</span>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                    </button>
                    <div class="language-dropdown">
                        <button class="language-option" data-language="en">English</button>
                        <button class="language-option" data-language="zh">中文</button>
                        <button class="language-option" data-language="ms">Bahasa</button>
                    </div>
                </div>
                
                <a href="login.html" class="login-btn btn btn-primary" data-translate="login">Login</a>
            </div>
            
            <button class="nav-toggle">☰</button>
        </nav>
    </header>

    <!-- Main Content -->
    <main style="margin-top: var(--header-height);">
        <!-- Demo Header -->
        <section class="demo-header section" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); color: var(--white);">
            <div class="container text-center">
                <h1 style="font-size: var(--text-4xl); font-weight: 700; margin-bottom: var(--space-4); color: var(--white);">
                    🎉 EatMY Enhanced Features Demo
                </h1>
                <p style="font-size: var(--text-lg); color: rgba(255, 255, 255, 0.9); max-width: 600px; margin: 0 auto;">
                    Explore the latest improvements to Malaysia's premier food review platform
                </p>
            </div>
        </section>

        <!-- New Features -->
        <section class="features-demo section">
            <div class="container">
                <h2 style="text-align: center; margin-bottom: var(--space-12);">✨ What's New</h2>
                
                <div class="features-grid grid grid-cols-1 md:grid-cols-2 gap-12">
                    <!-- Enhanced Categories -->
                    <div class="feature-demo card" style="padding: var(--space-8); text-align: center;">
                        <div style="font-size: 4rem; margin-bottom: var(--space-4);">🗂️</div>
                        <h3 style="margin-bottom: var(--space-4);">Enhanced Category System</h3>
                        <p style="color: var(--gray-600); margin-bottom: var(--space-6);">
                            Browse restaurants by cuisine, region, occasion, and special features. 
                            Find exactly what you're looking for with our comprehensive categorization.
                        </p>
                        <div style="display: flex; flex-wrap: wrap; gap: var(--space-2); justify-content: center; margin-bottom: var(--space-6);">
                            <span class="badge" style="background: #e74c3c;">🍽️ Cuisine Types</span>
                            <span class="badge" style="background: #3498db;">📍 Regions</span>
                            <span class="badge" style="background: #f39c12;">🎉 Occasions</span>
                            <span class="badge" style="background: #27ae60;">✨ Special Features</span>
                        </div>
                        <a href="categories.html" class="btn btn-primary">Explore Categories</a>
                    </div>

                    <!-- Luxury Rankings -->
                    <div class="feature-demo card" style="padding: var(--space-8); text-align: center;">
                        <div style="font-size: 4rem; margin-bottom: var(--space-4);">👑</div>
                        <h3 style="margin-bottom: var(--space-4);">Luxury Elite Rankings</h3>
                        <p style="color: var(--gray-600); margin-bottom: var(--space-6);">
                            Experience our redesigned rankings with premium styling, 
                            detailed restaurant profiles, and exclusive elite categories.
                        </p>
                        <div style="display: flex; flex-wrap: wrap; gap: var(--space-2); justify-content: center; margin-bottom: var(--space-6);">
                            <span class="badge" style="background: #FFD700; color: #1a1a1a;">🏆 Elite 50</span>
                            <span class="badge" style="background: #9b59b6;">⭐ Rising Stars</span>
                            <span class="badge" style="background: #e91e63;">💎 Hidden Gems</span>
                            <span class="badge" style="background: #34495e;">🥂 Haute Cuisine</span>
                        </div>
                        <a href="rankings.html" class="btn btn-primary">View Rankings</a>
                    </div>

                    <!-- More Restaurants -->
                    <div class="feature-demo card" style="padding: var(--space-8); text-align: center;">
                        <div style="font-size: 4rem; margin-bottom: var(--space-4);">🍽️</div>
                        <h3 style="margin-bottom: var(--space-4);">Expanded Restaurant Database</h3>
                        <p style="color: var(--gray-600); margin-bottom: var(--space-6);">
                            Discover more restaurants across Malaysia with high-quality photos from Unsplash, 
                            detailed information, and comprehensive categorization.
                        </p>
                        <div style="display: flex; flex-wrap: wrap; gap: var(--space-2); justify-content: center; margin-bottom: var(--space-6);">
                            <span class="badge" style="background: #2ecc71;">10+ Restaurants</span>
                            <span class="badge" style="background: #e67e22;">Real Photos</span>
                            <span class="badge" style="background: #9b59b6;">Multiple Regions</span>
                        </div>
                        <a href="search.html" class="btn btn-primary">Browse Restaurants</a>
                    </div>

                    <!-- Enhanced Search -->
                    <div class="feature-demo card" style="padding: var(--space-8); text-align: center;">
                        <div style="font-size: 4rem; margin-bottom: var(--space-4);">🔍</div>
                        <h3 style="margin-bottom: var(--space-4);">Advanced Search & Filtering</h3>
                        <p style="color: var(--gray-600); margin-bottom: var(--space-6);">
                            Find restaurants with our enhanced search system supporting 
                            multiple filter types including dietary preferences and special amenities.
                        </p>
                        <div style="display: flex; flex-wrap: wrap; gap: var(--space-2); justify-content: center; margin-bottom: var(--space-6);">
                            <span class="badge" style="background: #27ae60;">✅ Halal</span>
                            <span class="badge" style="background: #2ecc71;">🌱 Vegetarian</span>
                            <span class="badge" style="background: #3498db;">📶 WiFi</span>
                            <span class="badge" style="background: #e74c3c;">🚚 Delivery</span>
                        </div>
                        <a href="search.html" class="btn btn-primary">Try Search</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Navigation -->
        <section class="quick-nav section" style="background: var(--gray-50);">
            <div class="container">
                <h2 style="text-align: center; margin-bottom: var(--space-8);">🚀 Quick Navigation</h2>
                
                <div class="nav-grid grid grid-cols-2 md:grid-cols-4 gap-6">
                    <a href="index.html" class="nav-card card" style="padding: var(--space-6); text-align: center; text-decoration: none; color: inherit; transition: all var(--transition-fast);">
                        <div style="font-size: 2.5rem; margin-bottom: var(--space-3);">🏠</div>
                        <h4>Homepage</h4>
                        <p style="color: var(--gray-600); font-size: var(--text-sm);">Featured restaurants</p>
                    </a>
                    
                    <a href="categories.html" class="nav-card card" style="padding: var(--space-6); text-align: center; text-decoration: none; color: inherit; transition: all var(--transition-fast);">
                        <div style="font-size: 2.5rem; margin-bottom: var(--space-3);">🗂️</div>
                        <h4>Categories</h4>
                        <p style="color: var(--gray-600); font-size: var(--text-sm);">Browse by type</p>
                    </a>
                    
                    <a href="rankings.html" class="nav-card card" style="padding: var(--space-6); text-align: center; text-decoration: none; color: inherit; transition: all var(--transition-fast);">
                        <div style="font-size: 2.5rem; margin-bottom: var(--space-3);">👑</div>
                        <h4>Rankings</h4>
                        <p style="color: var(--gray-600); font-size: var(--text-sm);">Elite dining</p>
                    </a>
                    
                    <a href="search.html" class="nav-card card" style="padding: var(--space-6); text-align: center; text-decoration: none; color: inherit; transition: all var(--transition-fast);">
                        <div style="font-size: 2.5rem; margin-bottom: var(--space-3);">🔍</div>
                        <h4>Search</h4>
                        <p style="color: var(--gray-600); font-size: var(--text-sm);">Find restaurants</p>
                    </a>
                </div>
            </div>
        </section>

        <!-- Demo Stats -->
        <section class="demo-stats section">
            <div class="container">
                <div class="stats-grid grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
                    <div class="stat-item">
                        <div style="font-size: var(--text-4xl); font-weight: 700; color: var(--primary-color); margin-bottom: var(--space-2);">
                            10+
                        </div>
                        <div style="color: var(--gray-600);">Restaurants Added</div>
                    </div>
                    
                    <div class="stat-item">
                        <div style="font-size: var(--text-4xl); font-weight: 700; color: var(--primary-color); margin-bottom: var(--space-2);">
                            4
                        </div>
                        <div style="color: var(--gray-600);">Category Types</div>
                    </div>
                    
                    <div class="stat-item">
                        <div style="font-size: var(--text-4xl); font-weight: 700; color: var(--primary-color); margin-bottom: var(--space-2);">
                            6
                        </div>
                        <div style="color: var(--gray-600);">Ranking Categories</div>
                    </div>
                    
                    <div class="stat-item">
                        <div style="font-size: var(--text-4xl); font-weight: 700; color: var(--primary-color); margin-bottom: var(--space-2);">
                            100%
                        </div>
                        <div style="color: var(--gray-600);">Real Photos</div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <!-- 加载数据文件（必须先加载） -->
    <script src="js/data.js"></script>
    <!-- 加载应用逻辑 -->
    <script src="js/main.js"></script>
    <script>
        // Add hover effects to navigation cards
        document.addEventListener('DOMContentLoaded', function() {
            const navCards = document.querySelectorAll('.nav-card');
            navCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                    this.style.boxShadow = 'var(--shadow-xl)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'var(--shadow-sm)';
                });
            });
        });
    </script>
</body>
</html>
