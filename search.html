<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Restaurants - EatMY</title>
    <meta name="description" content="Search and discover restaurants in Malaysia. Filter by cuisine, location, rating and more.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="index.html" class="nav-brand">EatMY</a>
            
            <ul class="nav-links">
                <li><a href="index.html" class="nav-link" data-translate="home">Home</a></li>
                <li><a href="search.html" class="nav-link active" data-translate="search">Search</a></li>
                <li><a href="rankings.html" class="nav-link" data-translate="rankings">Rankings</a></li>
                <li><a href="categories.html" class="nav-link" data-translate="categories">Categories</a></li>
                <li><a href="map.html" class="nav-link" data-translate="map">Map</a></li>
            </ul>
            
            <div class="nav-actions">
                <!-- Language Switcher -->
                <div class="language-switcher">
                    <button class="language-button">
                        <span>EN</span>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                    </button>
                    <div class="language-dropdown">
                        <button class="language-option" data-language="en">English</button>
                        <button class="language-option" data-language="zh">中文</button>
                        <button class="language-option" data-language="ms">Bahasa</button>
                    </div>
                </div>
                
                <!-- User Menu -->
                <div class="user-menu" style="display: none;">
                    <a href="profile.html" class="profile-link btn btn-outline">Profile</a>
                    <button onclick="EatMY.logout()" class="btn btn-secondary" data-translate="logout">Logout</button>
                </div>
                
                <!-- Login Button -->
                <a href="login.html" class="login-btn btn btn-primary" data-translate="login">Login</a>
            </div>
            
            <button class="nav-toggle">☰</button>
        </nav>
    </header>

    <!-- Main Content -->
    <main style="margin-top: var(--header-height);">
        <!-- Search Header -->
        <section class="search-header section-sm" style="background: var(--white); border-bottom: 1px solid var(--gray-200);">
            <div class="container">
                <div class="search-bar" style="max-width: 600px; margin: 0 auto;">
                    <form class="search-form">
                        <input 
                            type="text" 
                            class="search-input" 
                            id="main-search"
                            placeholder="Search restaurants, cuisines..."
                            data-translate="searchPlaceholder"
                        >
                        <button type="submit" class="search-button">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                            </svg>
                        </button>
                    </form>
                </div>
            </div>
        </section>

        <!-- Filters and Results -->
        <section class="search-content section-sm">
            <div class="container">
                <div class="search-layout" style="display: grid; grid-template-columns: 280px 1fr; gap: var(--space-8);">
                    <!-- Filters Sidebar -->
                    <aside class="filters-sidebar">
                        <div class="filters-container">
                            <h3 class="filters-title">Filters</h3>
                            
                            <!-- Cuisine Filter -->
                            <div class="filter-group">
                                <h4 class="filter-title">Cuisine Type</h4>
                                <div class="filter-options">
                                    <label class="filter-option">
                                        <input type="radio" name="cuisine" value="all" checked>
                                        <span class="filter-label">All Cuisines</span>
                                    </label>
                                    <label class="filter-option">
                                        <input type="radio" name="cuisine" value="malay">
                                        <span class="filter-label">Malay</span>
                                    </label>
                                    <label class="filter-option">
                                        <input type="radio" name="cuisine" value="chinese">
                                        <span class="filter-label">Chinese</span>
                                    </label>
                                    <label class="filter-option">
                                        <input type="radio" name="cuisine" value="indian">
                                        <span class="filter-label">Indian</span>
                                    </label>
                                    <label class="filter-option">
                                        <input type="radio" name="cuisine" value="western">
                                        <span class="filter-label">Western</span>
                                    </label>
                                    <label class="filter-option">
                                        <input type="radio" name="cuisine" value="japanese">
                                        <span class="filter-label">Japanese</span>
                                    </label>
                                    <label class="filter-option">
                                        <input type="radio" name="cuisine" value="korean">
                                        <span class="filter-label">Korean</span>
                                    </label>
                                    <label class="filter-option">
                                        <input type="radio" name="cuisine" value="thai">
                                        <span class="filter-label">Thai</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Area Filter -->
                            <div class="filter-group">
                                <h4 class="filter-title">Area</h4>
                                <select class="form-select" name="area" id="area-filter">
                                    <option value="all">All Areas</option>
                                    <option value="Kuala Lumpur">Kuala Lumpur</option>
                                    <option value="Selangor">Selangor</option>
                                    <option value="Penang">Penang</option>
                                    <option value="Johor">Johor</option>
                                    <option value="Perak">Perak</option>
                                    <option value="Kedah">Kedah</option>
                                </select>
                            </div>

                            <!-- Rating Filter -->
                            <div class="filter-group">
                                <h4 class="filter-title">Minimum Rating</h4>
                                <div class="filter-options">
                                    <label class="filter-option">
                                        <input type="radio" name="rating" value="0" checked>
                                        <span class="filter-label">Any Rating</span>
                                    </label>
                                    <label class="filter-option">
                                        <input type="radio" name="rating" value="4">
                                        <span class="filter-label">4+ Stars</span>
                                    </label>
                                    <label class="filter-option">
                                        <input type="radio" name="rating" value="4.5">
                                        <span class="filter-label">4.5+ Stars</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Special Filters -->
                            <div class="filter-group">
                                <h4 class="filter-title">Special Options</h4>
                                <div class="filter-options">
                                    <label class="filter-option">
                                        <input type="checkbox" name="halal" id="halal-filter">
                                        <span class="filter-label">Halal Certified</span>
                                    </label>
                                    <label class="filter-option">
                                        <input type="checkbox" name="open-now" id="open-now-filter">
                                        <span class="filter-label">Open Now</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Clear Filters -->
                            <button type="button" class="btn btn-outline" id="clear-filters" style="width: 100%; margin-top: var(--space-4);">
                                Clear All Filters
                            </button>
                        </div>
                    </aside>

                    <!-- Results Area -->
                    <main class="search-results">
                        <!-- Results Header -->
                        <div class="results-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-6);">
                            <div class="results-info">
                                <h2 class="results-title">Search Results</h2>
                                <p class="results-count text-gray-600">Found <span id="results-count">0</span> restaurants</p>
                            </div>
                            
                            <div class="results-controls" style="display: flex; gap: var(--space-4); align-items: center;">
                                <!-- Sort Options -->
                                <select class="form-select" name="sort" id="sort-select" style="width: auto;">
                                    <option value="relevance">Sort by Relevance</option>
                                    <option value="rating">Highest Rated</option>
                                    <option value="reviews">Most Reviews</option>
                                    <option value="name">Name A-Z</option>
                                </select>
                                
                                <!-- View Toggle -->
                                <div class="view-toggle" style="display: flex; border: 1px solid var(--gray-300); border-radius: var(--radius-md); overflow: hidden;">
                                    <button class="view-btn active" data-view="grid" style="padding: var(--space-2) var(--space-3); border: none; background: var(--primary-color); color: white;">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <rect x="3" y="3" width="7" height="7"></rect>
                                            <rect x="14" y="3" width="7" height="7"></rect>
                                            <rect x="14" y="14" width="7" height="7"></rect>
                                            <rect x="3" y="14" width="7" height="7"></rect>
                                        </svg>
                                    </button>
                                    <button class="view-btn" data-view="list" style="padding: var(--space-2) var(--space-3); border: none; background: white; color: var(--gray-600);">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <line x1="8" y1="6" x2="21" y2="6"></line>
                                            <line x1="8" y1="12" x2="21" y2="12"></line>
                                            <line x1="8" y1="18" x2="21" y2="18"></line>
                                            <line x1="3" y1="6" x2="3.01" y2="6"></line>
                                            <line x1="3" y1="12" x2="3.01" y2="12"></line>
                                            <line x1="3" y1="18" x2="3.01" y2="18"></line>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Results Grid -->
                        <div class="results-container">
                            <div class="results-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="results-grid">
                                <!-- Results will be populated by JavaScript -->
                            </div>
                            
                            <!-- No Results Message -->
                            <div class="no-results" id="no-results" style="display: none; text-align: center; padding: var(--space-16);">
                                <div style="font-size: 64px; margin-bottom: var(--space-4);">🔍</div>
                                <h3>No restaurants found</h3>
                                <p class="text-gray-600">Try adjusting your search criteria or filters</p>
                            </div>
                            
                            <!-- Loading State -->
                            <div class="loading-state" id="loading-state" style="display: none; text-align: center; padding: var(--space-16);">
                                <div class="spinner" style="margin: 0 auto var(--space-4);"></div>
                                <p>Searching restaurants...</p>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div class="pagination" id="pagination" style="display: flex; justify-content: center; margin-top: var(--space-8);">
                            <!-- Pagination will be populated by JavaScript -->
                        </div>
                    </main>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <!-- 加载数据文件（必须先加载） -->
    <script src="js/data.js"></script>
    <!-- 加载应用逻辑 -->
    <script src="js/main.js"></script>
    <script src="js/search.js"></script>
</body>
</html>
