// EatMY - Search Page Functionality
// 使用全局变量而不是ES6模块导入，避免CORS问题

// Search state
let currentResults = [];
let currentFilters = {
  query: '',
  cuisine: 'all',
  area: 'all',
  rating: 0,
  halal: false,
  openNow: false,
  sortBy: 'relevance'
};
let currentView = 'grid';
let currentPage = 1;
const resultsPerPage = 12;

// Initialize search page
document.addEventListener('DOMContentLoaded', function() {
  initializeSearchPage();
});

function initializeSearchPage() {
  // Parse URL parameters
  parseUrlParameters();

  // Initialize filters
  initializeFilters();

  // Initialize view toggle
  initializeViewToggle();

  // Initialize search form
  initializeSearchForm();

  // Perform initial search
  performSearch();

  console.log('Search page initialized');
}

// Perform search with current filters
function performSearch() {
  // 确保restaurants数据已加载
  if (!window.restaurants) {
    console.error('Restaurant data not loaded yet');
    return;
  }

  let results = [...window.restaurants];

  // Apply search query
  if (currentFilters.query) {
    const query = currentFilters.query.toLowerCase();
    results = results.filter(restaurant =>
      restaurant.name.toLowerCase().includes(query) ||
      restaurant.cuisine.toLowerCase().includes(query) ||
      restaurant.area.toLowerCase().includes(query) ||
      restaurant.description.toLowerCase().includes(query) ||
      restaurant.specialties.some(specialty =>
        specialty.toLowerCase().includes(query)
      )
    );
  }

  // Apply cuisine filter
  if (currentFilters.cuisine !== 'all') {
    results = results.filter(restaurant =>
      restaurant.cuisine === currentFilters.cuisine
    );
  }

  // Apply area filter
  if (currentFilters.area !== 'all') {
    results = results.filter(restaurant =>
      restaurant.area.toLowerCase().includes(currentFilters.area.toLowerCase())
    );
  }

  // Apply rating filter
  if (currentFilters.rating > 0) {
    results = results.filter(restaurant =>
      restaurant.rating >= currentFilters.rating
    );
  }

  // Apply halal filter
  if (currentFilters.halal) {
    results = results.filter(restaurant => restaurant.isHalal);
  }

  // Apply open now filter (simplified - assume all are open for demo)
  if (currentFilters.openNow) {
    results = results.filter(restaurant => restaurant.isOpen);
  }

  // Sort results
  sortResults(results);

  // Update current results
  currentResults = results;

  // Display results
  displayResults();
}

// Sort results based on current sort option
function sortResults(results) {
  switch (currentFilters.sortBy) {
    case 'rating':
      results.sort((a, b) => b.rating - a.rating);
      break;
    case 'reviews':
      results.sort((a, b) => b.reviewCount - a.reviewCount);
      break;
    case 'name':
      results.sort((a, b) => a.name.localeCompare(b.name));
      break;
    default:
      // Keep original order for relevance
      break;
  }
}

// Display search results
function displayResults() {
  const resultsGrid = document.getElementById('results-grid');
  const resultsCount = document.getElementById('results-count');
  const noResults = document.getElementById('no-results');

  if (!resultsGrid) return;

  // Update results count
  if (resultsCount) {
    resultsCount.textContent = currentResults.length;
  }

  // Show/hide no results message
  if (noResults) {
    noResults.style.display = currentResults.length === 0 ? 'block' : 'none';
  }

  // Clear existing results
  resultsGrid.innerHTML = '';

  // Show/hide results grid
  resultsGrid.style.display = currentResults.length === 0 ? 'none' : '';

  // Render restaurant cards
  currentResults.forEach(restaurant => {
    const card = createRestaurantCard(restaurant);
    resultsGrid.appendChild(card);
  });
}

// Create restaurant card element
function createRestaurantCard(restaurant) {
  const card = document.createElement('div');
  card.className = 'restaurant-card card';
  card.style.cursor = 'pointer';
  card.style.transition = 'all var(--transition-fast)';

  // Generate star rating
  const stars = generateStars(restaurant.rating);

  // Format cuisine name
  const cuisineName = formatCuisine(restaurant.cuisine);

  // Create badges HTML
  const badgesHtml = [
    restaurant.isHalal ? '<span class="badge" style="background: #27ae60; color: white; font-size: 0.75rem;">Halal</span>' : '',
    restaurant.badges ? restaurant.badges.slice(0, 2).map(badge =>
      `<span class="badge" style="background: var(--primary-color); color: white; font-size: 0.75rem;">${badge}</span>`
    ).join('') : ''
  ].filter(Boolean).join('');

  card.innerHTML = `
    <div class="restaurant-image" style="position: relative; height: 200px; overflow: hidden; border-radius: var(--radius-lg) var(--radius-lg) 0 0;">
      <img src="${restaurant.image}" alt="${restaurant.name}"
           style="width: 100%; height: 100%; object-fit: cover;" loading="lazy">
      <div class="restaurant-badges" style="position: absolute; top: var(--space-3); right: var(--space-3); display: flex; flex-direction: column; gap: var(--space-2);">
        ${badgesHtml}
      </div>
    </div>
    <div class="restaurant-info" style="padding: var(--space-4);">
      <h3 class="restaurant-name" style="font-size: var(--text-lg); font-weight: 600; margin-bottom: var(--space-2); color: var(--gray-900);">
        ${restaurant.name}
      </h3>
      <p class="restaurant-cuisine" style="color: var(--gray-600); margin-bottom: var(--space-3); font-size: var(--text-sm);">
        ${cuisineName} • ${restaurant.area}
      </p>
      <div class="restaurant-rating" style="display: flex; align-items: center; gap: var(--space-2); margin-bottom: var(--space-3);">
        <div class="stars" style="color: #fbbf24; font-size: var(--text-sm);">
          ${stars}
        </div>
        <span class="rating-text" style="color: var(--gray-600); font-size: var(--text-sm);">
          ${restaurant.rating} (${restaurant.reviewCount} reviews)
        </span>
      </div>
      <p class="restaurant-price" style="font-weight: 600; color: var(--primary-color); margin-bottom: var(--space-3);">
        ${restaurant.priceRange}
      </p>
      <div class="restaurant-tags" style="display: flex; flex-wrap: wrap; gap: var(--space-2);">
        ${restaurant.specialties.slice(0, 3).map(specialty =>
          `<span class="tag" style="background: var(--gray-100); color: var(--gray-700); padding: var(--space-1) var(--space-2); border-radius: var(--radius-full); font-size: 0.75rem;">${specialty}</span>`
        ).join('')}
      </div>
    </div>
  `;

  // Add hover effects
  card.addEventListener('mouseenter', () => {
    card.style.transform = 'translateY(-4px)';
    card.style.boxShadow = 'var(--shadow-xl)';
  });

  card.addEventListener('mouseleave', () => {
    card.style.transform = 'translateY(0)';
    card.style.boxShadow = 'var(--shadow-sm)';
  });

  // Add click handler
  card.addEventListener('click', () => {
    window.location.href = `restaurant.html?id=${restaurant.id}`;
  });

  return card;
}

// Parse URL parameters
function parseUrlParameters() {
  const urlParams = new URLSearchParams(window.location.search);
  
  // Set search query
  const query = urlParams.get('q');
  if (query) {
    currentFilters.query = query;
    const searchInput = document.getElementById('main-search');
    if (searchInput) {
      searchInput.value = query;
    }
  }
  
  // Set category filter
  const category = urlParams.get('category');
  if (category) {
    currentFilters.cuisine = category;
  }
  
  // Set area filter
  const area = urlParams.get('area');
  if (area) {
    currentFilters.area = area;
  }
}

// Initialize filters
function initializeFilters() {
  // Set initial filter values from URL
  if (currentFilters.cuisine !== 'all') {
    const cuisineRadio = document.querySelector(`input[name="cuisine"][value="${currentFilters.cuisine}"]`);
    if (cuisineRadio) {
      cuisineRadio.checked = true;
    }
  }
  
  if (currentFilters.area !== 'all') {
    const areaSelect = document.getElementById('area-filter');
    if (areaSelect) {
      areaSelect.value = currentFilters.area;
    }
  }
  
  // Add event listeners for filters
  const cuisineRadios = document.querySelectorAll('input[name="cuisine"]');
  cuisineRadios.forEach(radio => {
    radio.addEventListener('change', function() {
      currentFilters.cuisine = this.value;
      performSearch();
    });
  });
  
  const areaSelect = document.getElementById('area-filter');
  if (areaSelect) {
    areaSelect.addEventListener('change', function() {
      currentFilters.area = this.value;
      performSearch();
    });
  }
  
  const ratingRadios = document.querySelectorAll('input[name="rating"]');
  ratingRadios.forEach(radio => {
    radio.addEventListener('change', function() {
      currentFilters.rating = parseFloat(this.value);
      performSearch();
    });
  });
  
  const halalCheckbox = document.getElementById('halal-filter');
  if (halalCheckbox) {
    halalCheckbox.addEventListener('change', function() {
      currentFilters.halal = this.checked;
      performSearch();
    });
  }
  
  const openNowCheckbox = document.getElementById('open-now-filter');
  if (openNowCheckbox) {
    openNowCheckbox.addEventListener('change', function() {
      currentFilters.openNow = this.checked;
      performSearch();
    });
  }
  
  const sortSelect = document.getElementById('sort-select');
  if (sortSelect) {
    sortSelect.addEventListener('change', function() {
      currentFilters.sortBy = this.value;
      performSearch();
    });
  }
  
  const clearFiltersBtn = document.getElementById('clear-filters');
  if (clearFiltersBtn) {
    clearFiltersBtn.addEventListener('click', clearAllFilters);
  }
}

// Initialize search form
function initializeSearchForm() {
  const searchInput = document.getElementById('main-search');
  if (searchInput) {
    searchInput.addEventListener('input', function() {
      currentFilters.query = this.value;
      // Debounce search
      clearTimeout(window.searchTimeout);
      window.searchTimeout = setTimeout(() => {
        performSearch();
      }, 300);
    });
  }
}

// Generate star rating HTML
function generateStars(rating) {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  return '★'.repeat(fullStars) +
         (hasHalfStar ? '☆' : '') +
         '☆'.repeat(emptyStars);
}

// Format cuisine name
function formatCuisine(cuisine) {
  const cuisineMap = {
    'malay': 'Malay',
    'chinese': 'Chinese',
    'indian': 'Indian',
    'western': 'Western',
    'japanese': 'Japanese',
    'korean': 'Korean',
    'thai': 'Thai',
    'italian': 'Italian'
  };
  return cuisineMap[cuisine] || cuisine.charAt(0).toUpperCase() + cuisine.slice(1);
}

// Switch view between grid and list
function switchView(view) {
  currentView = view;
  const resultsGrid = document.getElementById('results-grid');
  const viewButtons = document.querySelectorAll('.view-btn');

  // Update button states
  viewButtons.forEach(btn => {
    if (btn.dataset.view === view) {
      btn.classList.add('active');
      btn.style.background = 'var(--primary-color)';
      btn.style.color = 'white';
    } else {
      btn.classList.remove('active');
      btn.style.background = 'white';
      btn.style.color = 'var(--gray-600)';
    }
  });

  // Update grid layout
  if (resultsGrid) {
    if (view === 'list') {
      resultsGrid.className = 'results-list';
      resultsGrid.style.display = 'flex';
      resultsGrid.style.flexDirection = 'column';
      resultsGrid.style.gap = 'var(--space-4)';
    } else {
      resultsGrid.className = 'results-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
      resultsGrid.style.display = '';
      resultsGrid.style.flexDirection = '';
    }
  }
}

// Clear all filters
function clearAllFilters() {
  // Reset filter state
  currentFilters = {
    query: '',
    cuisine: 'all',
    area: 'all',
    rating: 0,
    halal: false,
    openNow: false,
    sortBy: 'relevance'
  };

  // Reset form elements
  const searchInput = document.getElementById('main-search');
  if (searchInput) searchInput.value = '';

  // Reset cuisine radio buttons
  document.querySelectorAll('input[name="cuisine"]').forEach(radio => {
    radio.checked = radio.value === 'all';
  });

  // Reset area select
  const areaSelect = document.getElementById('area-filter');
  if (areaSelect) areaSelect.value = 'all';

  // Reset rating radio buttons
  document.querySelectorAll('input[name="rating"]').forEach(radio => {
    radio.checked = radio.value === '0';
  });

  // Reset checkboxes
  const halalCheckbox = document.getElementById('halal-filter');
  if (halalCheckbox) halalCheckbox.checked = false;

  const openNowCheckbox = document.getElementById('open-now-filter');
  if (openNowCheckbox) openNowCheckbox.checked = false;

  // Reset sort
  const sortSelect = document.getElementById('sort-select');
  if (sortSelect) sortSelect.value = 'relevance';

  // Perform search with cleared filters
  performSearch();
}

// Initialize view toggle
function initializeViewToggle() {
  const viewButtons = document.querySelectorAll('.view-btn');
  viewButtons.forEach(btn => {
    btn.addEventListener('click', function() {
      const view = this.dataset.view;
      switchView(view);
    });
  });
}

// Initialize search manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // 等待数据加载完成
  setTimeout(() => {
    if (window.restaurants && window.restaurants.length > 0) {
      console.log('Restaurant data loaded, initializing search page...');
      initializeSearchPage();
    } else {
      console.log('Waiting for restaurant data...');
      // 再等待一下
      setTimeout(() => {
        if (window.restaurants && window.restaurants.length > 0) {
          initializeSearchPage();
        } else {
          console.error('Restaurant data failed to load');
          // 即使没有数据也初始化页面
          initializeSearchPage();
        }
      }, 500);
    }
  }, 100);
});








