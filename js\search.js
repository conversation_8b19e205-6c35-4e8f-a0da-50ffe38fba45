// EatMY - Search Page Functionality

import { searchRestaurants, restaurants } from './data.js';

// Search state
let currentResults = [];
let currentFilters = {
  query: '',
  cuisine: 'all',
  area: 'all',
  rating: 0,
  halal: false,
  openNow: false,
  sortBy: 'relevance'
};
let currentView = 'grid';
let currentPage = 1;
const resultsPerPage = 12;

// Initialize search page
document.addEventListener('DOMContentLoaded', function() {
  initializeSearchPage();
});

function initializeSearchPage() {
  // Parse URL parameters
  parseUrlParameters();
  
  // Initialize filters
  initializeFilters();
  
  // Initialize view toggle
  initializeViewToggle();
  
  // Initialize search form
  initializeSearchForm();
  
  // Perform initial search
  performSearch();
  
  console.log('Search page initialized');
}

// Parse URL parameters
function parseUrlParameters() {
  const urlParams = new URLSearchParams(window.location.search);
  
  // Set search query
  const query = urlParams.get('q');
  if (query) {
    currentFilters.query = query;
    const searchInput = document.getElementById('main-search');
    if (searchInput) {
      searchInput.value = query;
    }
  }
  
  // Set category filter
  const category = urlParams.get('category');
  if (category) {
    currentFilters.cuisine = category;
  }
  
  // Set area filter
  const area = urlParams.get('area');
  if (area) {
    currentFilters.area = area;
  }
}

// Initialize filters
function initializeFilters() {
  // Set initial filter values from URL
  if (currentFilters.cuisine !== 'all') {
    const cuisineRadio = document.querySelector(`input[name="cuisine"][value="${currentFilters.cuisine}"]`);
    if (cuisineRadio) {
      cuisineRadio.checked = true;
    }
  }
  
  if (currentFilters.area !== 'all') {
    const areaSelect = document.getElementById('area-filter');
    if (areaSelect) {
      areaSelect.value = currentFilters.area;
    }
  }
  
  // Add event listeners for filters
  const cuisineRadios = document.querySelectorAll('input[name="cuisine"]');
  cuisineRadios.forEach(radio => {
    radio.addEventListener('change', function() {
      currentFilters.cuisine = this.value;
      performSearch();
    });
  });
  
  const areaSelect = document.getElementById('area-filter');
  if (areaSelect) {
    areaSelect.addEventListener('change', function() {
      currentFilters.area = this.value;
      performSearch();
    });
  }
  
  const ratingRadios = document.querySelectorAll('input[name="rating"]');
  ratingRadios.forEach(radio => {
    radio.addEventListener('change', function() {
      currentFilters.rating = parseFloat(this.value);
      performSearch();
    });
  });
  
  const halalCheckbox = document.getElementById('halal-filter');
  if (halalCheckbox) {
    halalCheckbox.addEventListener('change', function() {
      currentFilters.halal = this.checked;
      performSearch();
    });
  }
  
  const openNowCheckbox = document.getElementById('open-now-filter');
  if (openNowCheckbox) {
    openNowCheckbox.addEventListener('change', function() {
      currentFilters.openNow = this.checked;
      performSearch();
    });
  }
  
  const sortSelect = document.getElementById('sort-select');
  if (sortSelect) {
    sortSelect.addEventListener('change', function() {
      currentFilters.sortBy = this.value;
      performSearch();
    });
  }
  
  const clearFiltersBtn = document.getElementById('clear-filters');
  if (clearFiltersBtn) {
    clearFiltersBtn.addEventListener('click', clearAllFilters);
  }
}

// Initialize view toggle
function initializeViewToggle() {
  const viewButtons = document.querySelectorAll('.view-btn');
  viewButtons.forEach(btn => {
    btn.addEventListener('click', function() {
      const view = this.dataset.view;
      switchView(view);
    });
  });
}

// Initialize search form
function initializeSearchForm() {
  const searchForm = document.querySelector('.search-form');
  if (searchForm) {
    searchForm.addEventListener('submit', function(e) {
      e.preventDefault();
      const query = document.getElementById('main-search').value.trim();
      currentFilters.query = query;
      currentPage = 1;
      performSearch();
    });
  }
}

// Perform search
function performSearch() {
  showLoading();
  
  // Simulate API delay
  setTimeout(() => {
    // Apply additional filters that aren't in the searchRestaurants function
    let results = searchRestaurants(currentFilters.query, currentFilters);
    
    // Apply open now filter
    if (currentFilters.openNow) {
      results = results.filter(restaurant => restaurant.isOpen);
    }
    
    currentResults = results;
    displayResults();
    updateResultsCount();
    hideLoading();
  }, 300);
}

// Display results
function displayResults() {
  const resultsGrid = document.getElementById('results-grid');
  const noResults = document.getElementById('no-results');
  
  if (currentResults.length === 0) {
    resultsGrid.style.display = 'none';
    noResults.style.display = 'block';
    return;
  }
  
  resultsGrid.style.display = 'grid';
  noResults.style.display = 'none';
  
  // Calculate pagination
  const startIndex = (currentPage - 1) * resultsPerPage;
  const endIndex = startIndex + resultsPerPage;
  const pageResults = currentResults.slice(startIndex, endIndex);
  
  // Render results
  if (currentView === 'grid') {
    resultsGrid.className = 'results-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
    resultsGrid.innerHTML = pageResults.map(restaurant => createRestaurantCard(restaurant)).join('');
  } else {
    resultsGrid.className = 'results-list';
    resultsGrid.innerHTML = pageResults.map(restaurant => createRestaurantListItem(restaurant)).join('');
  }
  
  // Initialize favorite buttons
  initializeFavoriteButtons();
  
  // Update pagination
  updatePagination();
}

// Create restaurant card
function createRestaurantCard(restaurant) {
  const isFavorite = window.EatMY.favorites.includes(restaurant.id);
  const badges = restaurant.badges.map(badge => 
    `<span class="badge badge-${badge}">${badge.replace('-', ' ')}</span>`
  ).join('');
  
  return `
    <div class="restaurant-card" onclick="window.location.href='restaurant-detail.html?id=${restaurant.id}'">
      <div class="restaurant-card-image-container" style="position: relative;">
        <img 
          src="${restaurant.image}" 
          alt="${restaurant.name}"
          class="restaurant-card-image"
          loading="lazy"
        >
        <button 
          class="favorite-btn" 
          data-restaurant-id="${restaurant.id}"
          onclick="event.stopPropagation(); window.EatMY.toggleFavorite(${restaurant.id})"
          style="position: absolute; top: 12px; right: 12px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 36px; height: 36px; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 16px;"
        >
          ${isFavorite ? '❤️' : '🤍'}
        </button>
        ${restaurant.isOpen ? 
          '<div class="status-badge open" style="position: absolute; bottom: 12px; left: 12px; background: var(--success); color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">Open Now</div>' : 
          '<div class="status-badge closed" style="position: absolute; bottom: 12px; left: 12px; background: var(--error); color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">Closed</div>'
        }
      </div>
      
      <div class="restaurant-card-content">
        <div class="restaurant-card-header" style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
          <h3 class="restaurant-card-title">${restaurant.name}</h3>
          ${restaurant.isHalal ? '<span class="badge badge-halal">Halal</span>' : ''}
        </div>
        
        <p class="restaurant-card-cuisine">${getCuisineDisplayName(restaurant.cuisine)} • ${restaurant.area}</p>
        
        <div class="restaurant-card-badges" style="margin-bottom: 12px;">
          ${badges}
        </div>
        
        <div class="restaurant-card-footer">
          <div class="rating">
            <div class="rating-stars">
              ${createStarRating(restaurant.rating)}
            </div>
            <span class="rating-value">${restaurant.rating}</span>
            <span class="rating-count">(${restaurant.reviewCount})</span>
          </div>
          
          <div class="price-range text-sm text-gray-600">
            ${restaurant.priceRange}
          </div>
        </div>
      </div>
    </div>
  `;
}

// Create restaurant list item
function createRestaurantListItem(restaurant) {
  const isFavorite = window.EatMY.favorites.includes(restaurant.id);
  const badges = restaurant.badges.map(badge => 
    `<span class="badge badge-${badge}">${badge.replace('-', ' ')}</span>`
  ).join('');
  
  return `
    <div class="restaurant-list-item" onclick="window.location.href='restaurant-detail.html?id=${restaurant.id}'" style="display: flex; background: white; border-radius: var(--radius-xl); box-shadow: var(--shadow-sm); margin-bottom: var(--space-4); overflow: hidden; cursor: pointer; transition: all var(--transition-fast);">
      <img 
        src="${restaurant.image}" 
        alt="${restaurant.name}"
        style="width: 200px; height: 150px; object-fit: cover;"
        loading="lazy"
      >
      
      <div style="flex: 1; padding: var(--space-4); display: flex; flex-direction: column; justify-content: space-between;">
        <div>
          <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: var(--space-2);">
            <h3 style="font-size: var(--text-xl); font-weight: 600; margin: 0;">${restaurant.name}</h3>
            <button 
              class="favorite-btn" 
              data-restaurant-id="${restaurant.id}"
              onclick="event.stopPropagation(); window.EatMY.toggleFavorite(${restaurant.id})"
              style="background: none; border: none; font-size: 20px; cursor: pointer;"
            >
              ${isFavorite ? '❤️' : '🤍'}
            </button>
          </div>
          
          <p style="color: var(--gray-600); margin-bottom: var(--space-2);">${getCuisineDisplayName(restaurant.cuisine)} • ${restaurant.area}</p>
          
          <div style="margin-bottom: var(--space-3);">
            ${badges}
            ${restaurant.isHalal ? '<span class="badge badge-halal">Halal</span>' : ''}
            ${restaurant.isOpen ? '<span class="badge badge-success">Open Now</span>' : '<span class="badge badge-warning">Closed</span>'}
          </div>
        </div>
        
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div class="rating">
            <div class="rating-stars">
              ${createStarRating(restaurant.rating)}
            </div>
            <span class="rating-value">${restaurant.rating}</span>
            <span class="rating-count">(${restaurant.reviewCount})</span>
          </div>
          
          <div class="price-range" style="color: var(--gray-600);">
            ${restaurant.priceRange}
          </div>
        </div>
      </div>
    </div>
  `;
}

// Switch view
function switchView(view) {
  currentView = view;
  
  // Update button states
  const viewButtons = document.querySelectorAll('.view-btn');
  viewButtons.forEach(btn => {
    if (btn.dataset.view === view) {
      btn.style.background = 'var(--primary-color)';
      btn.style.color = 'white';
    } else {
      btn.style.background = 'white';
      btn.style.color = 'var(--gray-600)';
    }
  });
  
  // Re-render results
  displayResults();
}

// Update results count
function updateResultsCount() {
  const countElement = document.getElementById('results-count');
  if (countElement) {
    countElement.textContent = currentResults.length;
  }
}

// Update pagination
function updatePagination() {
  const paginationContainer = document.getElementById('pagination');
  if (!paginationContainer) return;
  
  const totalPages = Math.ceil(currentResults.length / resultsPerPage);
  
  if (totalPages <= 1) {
    paginationContainer.innerHTML = '';
    return;
  }
  
  let paginationHTML = '';
  
  // Previous button
  if (currentPage > 1) {
    paginationHTML += `<button class="btn btn-outline" onclick="changePage(${currentPage - 1})">Previous</button>`;
  }
  
  // Page numbers
  for (let i = 1; i <= totalPages; i++) {
    if (i === currentPage) {
      paginationHTML += `<button class="btn btn-primary">${i}</button>`;
    } else {
      paginationHTML += `<button class="btn btn-outline" onclick="changePage(${i})">${i}</button>`;
    }
  }
  
  // Next button
  if (currentPage < totalPages) {
    paginationHTML += `<button class="btn btn-outline" onclick="changePage(${currentPage + 1})">Next</button>`;
  }
  
  paginationContainer.innerHTML = paginationHTML;
}

// Change page
function changePage(page) {
  currentPage = page;
  displayResults();
  
  // Scroll to top of results
  document.querySelector('.search-results').scrollIntoView({ behavior: 'smooth' });
}

// Clear all filters
function clearAllFilters() {
  // Reset filter state
  currentFilters = {
    query: currentFilters.query, // Keep search query
    cuisine: 'all',
    area: 'all',
    rating: 0,
    halal: false,
    openNow: false,
    sortBy: 'relevance'
  };
  
  // Reset form elements
  document.querySelector('input[name="cuisine"][value="all"]').checked = true;
  document.getElementById('area-filter').value = 'all';
  document.querySelector('input[name="rating"][value="0"]').checked = true;
  document.getElementById('halal-filter').checked = false;
  document.getElementById('open-now-filter').checked = false;
  document.getElementById('sort-select').value = 'relevance';
  
  // Perform search
  currentPage = 1;
  performSearch();
}

// Show loading state
function showLoading() {
  const loadingState = document.getElementById('loading-state');
  const resultsGrid = document.getElementById('results-grid');
  const noResults = document.getElementById('no-results');
  
  if (loadingState) loadingState.style.display = 'block';
  if (resultsGrid) resultsGrid.style.display = 'none';
  if (noResults) noResults.style.display = 'none';
}

// Hide loading state
function hideLoading() {
  const loadingState = document.getElementById('loading-state');
  if (loadingState) loadingState.style.display = 'none';
}

// Utility functions (imported from home.js concepts)
function createStarRating(rating) {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  let stars = '';
  
  for (let i = 0; i < fullStars; i++) {
    stars += '<span class="rating-star filled">★</span>';
  }
  
  if (hasHalfStar) {
    stars += '<span class="rating-star half">☆</span>';
  }
  
  for (let i = 0; i < emptyStars; i++) {
    stars += '<span class="rating-star">☆</span>';
  }
  
  return stars;
}

function getCuisineDisplayName(cuisine) {
  const cuisineMap = {
    'malay': 'Malay',
    'chinese': 'Chinese',
    'indian': 'Indian',
    'western': 'Western',
    'japanese': 'Japanese',
    'korean': 'Korean',
    'thai': 'Thai',
    'fusion': 'Fusion'
  };
  
  return cuisineMap[cuisine] || cuisine;
}

function initializeFavoriteButtons() {
  const favoriteButtons = document.querySelectorAll('.favorite-btn');
  favoriteButtons.forEach(button => {
    const restaurantId = parseInt(button.dataset.restaurantId);
    const isFavorite = window.EatMY.favorites.includes(restaurantId);
    
    button.innerHTML = isFavorite ? '❤️' : '🤍';
  });
}

// Make changePage available globally
window.changePage = changePage;
