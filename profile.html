<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - EatMY</title>
    <meta name="description" content="View your profile, reviews, favorites, and activity on EatMY.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="index.html" class="nav-brand">EatMY</a>
            
            <ul class="nav-links">
                <li><a href="index.html" class="nav-link" data-translate="home">Home</a></li>
                <li><a href="search.html" class="nav-link" data-translate="search">Search</a></li>
                <li><a href="rankings.html" class="nav-link" data-translate="rankings">Rankings</a></li>
                <li><a href="categories.html" class="nav-link" data-translate="categories">Categories</a></li>
                <li><a href="map.html" class="nav-link" data-translate="map">Map</a></li>
            </ul>
            
            <div class="nav-actions">
                <!-- Language Switcher -->
                <div class="language-switcher">
                    <button class="language-button">
                        <span>EN</span>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                    </button>
                    <div class="language-dropdown">
                        <button class="language-option" data-language="en">English</button>
                        <button class="language-option" data-language="zh">中文</button>
                        <button class="language-option" data-language="ms">Bahasa</button>
                    </div>
                </div>
                
                <!-- User Menu -->
                <div class="user-menu">
                    <button onclick="EatMY.logout()" class="btn btn-secondary" data-translate="logout">Logout</button>
                </div>
            </div>
            
            <button class="nav-toggle">☰</button>
        </nav>
    </header>

    <!-- Main Content -->
    <main style="margin-top: var(--header-height);">
        <!-- Profile Header -->
        <section class="profile-header-section section-sm">
            <div class="container">
                <div class="profile-header">
                    <div class="profile-info">
                        <img src="" alt="Profile Avatar" class="profile-avatar" id="profile-avatar">
                        <div class="profile-details">
                            <h1 id="profile-name">User Name</h1>
                            <p id="profile-level" style="color: var(--primary-color); font-weight: 500; margin-bottom: var(--space-2);">
                                Food Explorer
                            </p>
                            <p id="profile-join-date" style="color: var(--gray-600); font-size: var(--text-sm);">
                                Member since January 2023
                            </p>
                            
                            <div class="profile-stats">
                                <div class="profile-stat">
                                    <div class="profile-stat-value" id="profile-reviews-count">0</div>
                                    <div class="profile-stat-label">Reviews</div>
                                </div>
                                <div class="profile-stat">
                                    <div class="profile-stat-value" id="profile-favorites-count">0</div>
                                    <div class="profile-stat-label">Favorites</div>
                                </div>
                                <div class="profile-stat">
                                    <div class="profile-stat-value" id="profile-followers-count">0</div>
                                    <div class="profile-stat-label">Followers</div>
                                </div>
                                <div class="profile-stat">
                                    <div class="profile-stat-value" id="profile-following-count">0</div>
                                    <div class="profile-stat-label">Following</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Profile Tabs -->
        <section class="profile-tabs-section">
            <div class="container">
                <div class="profile-tabs">
                    <button class="profile-tab active" data-tab="reviews">My Reviews</button>
                    <button class="profile-tab" data-tab="favorites">Favorites</button>
                    <button class="profile-tab" data-tab="following">Following</button>
                    <button class="profile-tab" data-tab="settings">Settings</button>
                </div>
            </div>
        </section>

        <!-- Profile Content -->
        <section class="profile-content-section section">
            <div class="container">
                <!-- Reviews Tab -->
                <div class="profile-content active" id="reviews-tab">
                    <div class="tab-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--space-6);">
                        <h2>My Reviews</h2>
                        <a href="review.html" class="btn btn-primary">Write New Review</a>
                    </div>
                    
                    <div class="reviews-list" id="user-reviews-list">
                        <!-- Reviews will be populated by JavaScript -->
                    </div>
                    
                    <div class="no-content" id="no-reviews" style="display: none; text-align: center; padding: var(--space-16);">
                        <div style="font-size: 64px; margin-bottom: var(--space-4);">📝</div>
                        <h3>No Reviews Yet</h3>
                        <p class="text-gray-600">Share your experiences by writing your first review</p>
                        <a href="review.html" class="btn btn-primary" style="margin-top: var(--space-6);">Write First Review</a>
                    </div>
                </div>
                
                <!-- Favorites Tab -->
                <div class="profile-content" id="favorites-tab" style="display: none;">
                    <div class="tab-header" style="margin-bottom: var(--space-6);">
                        <h2>My Favorite Restaurants</h2>
                    </div>
                    
                    <div class="favorites-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="user-favorites-list">
                        <!-- Favorites will be populated by JavaScript -->
                    </div>
                    
                    <div class="no-content" id="no-favorites" style="display: none; text-align: center; padding: var(--space-16);">
                        <div style="font-size: 64px; margin-bottom: var(--space-4);">❤️</div>
                        <h3>No Favorites Yet</h3>
                        <p class="text-gray-600">Save your favorite restaurants to find them easily later</p>
                        <a href="search.html" class="btn btn-primary" style="margin-top: var(--space-6);">Explore Restaurants</a>
                    </div>
                </div>
                
                <!-- Following Tab -->
                <div class="profile-content" id="following-tab" style="display: none;">
                    <div class="tab-header" style="margin-bottom: var(--space-6);">
                        <h2>People You Follow</h2>
                    </div>
                    
                    <div class="following-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="user-following-list">
                        <!-- Following will be populated by JavaScript -->
                    </div>
                    
                    <div class="no-content" id="no-following" style="display: none; text-align: center; padding: var(--space-16);">
                        <div style="font-size: 64px; margin-bottom: var(--space-4);">👥</div>
                        <h3>Not Following Anyone Yet</h3>
                        <p class="text-gray-600">Follow other food enthusiasts to see their reviews and recommendations</p>
                        <a href="search.html" class="btn btn-primary" style="margin-top: var(--space-6);">Discover People</a>
                    </div>
                </div>
                
                <!-- Settings Tab -->
                <div class="profile-content" id="settings-tab" style="display: none;">
                    <div class="tab-header" style="margin-bottom: var(--space-6);">
                        <h2>Account Settings</h2>
                    </div>
                    
                    <form class="settings-form" id="profile-settings-form">
                        <div class="form-section" style="margin-bottom: var(--space-8);">
                            <h3 style="margin-bottom: var(--space-4);">Profile Information</h3>
                            
                            <div class="form-group">
                                <label class="form-label" for="settings-name">Full Name</label>
                                <input type="text" class="form-input" id="settings-name" name="name" placeholder="Your full name">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="settings-email">Email Address</label>
                                <input type="email" class="form-input" id="settings-email" name="email" placeholder="Your email address">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="settings-bio">Bio</label>
                                <textarea class="form-textarea" id="settings-bio" name="bio" placeholder="Tell us about yourself and your food preferences"></textarea>
                            </div>
                        </div>
                        
                        <div class="form-section" style="margin-bottom: var(--space-8);">
                            <h3 style="margin-bottom: var(--space-4);">Preferences</h3>
                            
                            <div class="form-group">
                                <label class="form-label">Email Notifications</label>
                                <div style="display: flex; flex-direction: column; gap: var(--space-2);">
                                    <label style="display: flex; align-items: center; gap: var(--space-2); cursor: pointer;">
                                        <input type="checkbox" name="notify_reviews" checked>
                                        <span>When someone replies to my reviews</span>
                                    </label>
                                    <label style="display: flex; align-items: center; gap: var(--space-2); cursor: pointer;">
                                        <input type="checkbox" name="notify_likes" checked>
                                        <span>When someone likes my reviews</span>
                                    </label>
                                    <label style="display: flex; align-items: center; gap: var(--space-2); cursor: pointer;">
                                        <input type="checkbox" name="notify_follows">
                                        <span>When someone follows me</span>
                                    </label>
                                    <label style="display: flex; align-items: center; gap: var(--space-2); cursor: pointer;">
                                        <input type="checkbox" name="notify_newsletter" checked>
                                        <span>EatMY newsletter and updates</span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="settings-language">Preferred Language</label>
                                <select class="form-select" id="settings-language" name="language">
                                    <option value="en">English</option>
                                    <option value="zh">Chinese</option>
                                    <option value="ms">Bahasa Malaysia</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-section" style="margin-bottom: var(--space-8);">
                            <h3 style="margin-bottom: var(--space-4);">Security</h3>
                            
                            <div class="form-group">
                                <label class="form-label" for="settings-current-password">Current Password</label>
                                <input type="password" class="form-input" id="settings-current-password" name="current_password" placeholder="Enter your current password">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="settings-new-password">New Password</label>
                                <input type="password" class="form-input" id="settings-new-password" name="new_password" placeholder="Enter new password">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="settings-confirm-password">Confirm New Password</label>
                                <input type="password" class="form-input" id="settings-confirm-password" name="confirm_password" placeholder="Confirm new password">
                            </div>
                        </div>
                        
                        <div class="form-actions" style="display: flex; gap: var(--space-4); justify-content: flex-end;">
                            <button type="button" class="btn btn-secondary" onclick="resetForm()">Cancel</button>
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <!-- JavaScript -->
    <script type="module" src="js/main.js"></script>
    <script type="module" src="js/profile.js"></script>
</body>
</html>
