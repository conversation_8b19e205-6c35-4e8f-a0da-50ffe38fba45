
________________________________________
🧭 一、项目概况
项目名称：EatMY??MyFoodie
项目类型：马来西亚本地化餐厅点评与美食推荐平台
目标用户：
•	马来西亚本地年轻人、美食爱好者
•	海外游客
•	中小型餐饮商户
核心价值主张：打造“美食文化社区 + 商户变现工具 + 多语言本地体验”三位一体平台。(参考论坛/社区)
「打造马来西亚最具影响力的在地餐饮点评生态平台，兼顾内容社区与商户增长。」
________________________________________
🧱 二、系统结构概览
🎯 功能模块
A. 用户端功能
•	首页（推荐内容 + 搜索入口）
•	搜索页（筛选器 + 结果展示）
•	榜单页（Top Picks / 百大餐厅 / 节庆推荐）
•	餐厅详情页（评分、评论、菜单、地图）
o	菜单图文
o	多评分维度：味道 / 环境 / 服务
o	营业时间
o	Google Map 定位 + 路线导航按钮
•	用户评论系统（图文评论、收藏、点赞、评分维度：味道/环境/服务）
o	图文 + 星级评分
o	收藏 / 点赞 / 回复评论（楼中楼）
o	举报按钮
•	登录/注册模块（JWT认证 + 社交登录）
•	地图探索页（Google Maps + Marker 弹窗）
•	用户个人页（我评论过的 / 收藏的 / 关注的）
o	我的评论
o	我的收藏
•	用户等级与勋章系统
•	多语言切换（英文 / 中文 / 马来文）
•	通知中心（你被回复、被点赞、评论被精选时提醒）后期
•	食评达人认证机制（让用户争取优质评论身份）后期
B. 商户端功能（可选拓展）
•	商户信息页（菜单、营业时间、优惠展示）
•	商户等级（评分数/活跃度）
•	商户勋章（年度最活跃商户、图文丰富奖）
•	商户后台（信息管理、数据查看、活动发布、评论查看）
o	营业信息管理
o	活动发布（如节日优惠）
o	评论查看（不可删，只读 + 回复功能可选）
•	广告投放申请（首页推荐 / 搜索结果置顶）
•	数据分析仪表板（浏览量、收藏数、用户画像）
•	优惠券功能（后期引流用）
•	申请入驻审核机制（提前做好认证流程）
C. 管理后台功能
•	餐厅管理：增删改查、分类、标签管理、清真认证标记
•	评论管理：审核、屏蔽、用户举报处理
•	榜单管理：编辑精选、地区热门榜单管理
•	用户管理：查看、禁言、封号、达人认证
•	内容策展系统：节庆美食、亲子推荐、隐藏名店等内容专区配置
•	运营工具面板（活动管理、官方公告）
•	商户认证管理（上传执照 / 后台审核）
📦 D. 系统通用支持模块（底层）
•	多语言切换（Laravel + React 双端支持）
•	权限系统（用户 / 商户 / 管理员）
•	JWT 授权（Laravel Passport）
•	搜索引擎（Meilisearch，用于餐厅+评论全文搜索）
•	文件存储（Cloudinary -> 后期 AWS S3）
•	地图服务（Google Maps + Directions + Marker Cluster）
•	消息通知系统（OneSignal）
•	用户行为日志（user_activities）
•	推荐引擎（后期基于收藏 / 点赞 / 浏览推荐餐厅）
•	审计日志系统：记录后台每一次敏感操作（如改分、封号）
•	缓存策略：热点榜单、热门评论等建议 Redis 缓存

________________________________________
🛠️ 核心功能细节
•	用户认证：JWT 登录注册
•	评论系统：文本 + 图片上传 + 星级评分（味道/环境/服务）
•	搜索系统：关键词、标签、Halal筛选、地区、菜系等复合筛选
•	地图功能：Google Maps Marker + 弹窗 + 定位搜索 + 区域聚合
•	榜单机制：后台自定义榜单、用户浏览排行、节日特辑榜单
•	收藏系统：支持创建多组收藏夹
•	用户等级系统：评论数驱动称号、勋章系统设计
•	多语言支持：英文/中文/马来文切换，界面与内容翻译预留
•	响应式设计：适配移动端、iPad、桌面浏览
•	社交互动：点赞、关注用户、通知提醒系统
•	🏆 餐厅等级系统（Restaurant Reputation & Level）
o	每家餐厅根据评论数、收藏、评分等生成 rep_score
o	映射为等级（Lv.1–Lv.5）：如“人气餐厅”“旗舰名店”等
o	后台查看等级变动日志，用于运营管理
•	🏅 餐厅勋章系统（Restaurant Badge System）
o	勋章类型如：高评分餐厅、上榜餐厅、清真认证、高互动、高图文质量等
o	前台卡片页 + 详情页展示徽章图标（最多3个）
o	后台手动/自动授予 + 勋章维护接口支持
•	
________________________________________
 七、亮点
•	支持中英文/马来语切换，适配多语使用者
•	清真餐厅标识，方便马来用户筛选
•	Google 地图定位，地图互动找餐厅
•	榜单机制吸引流量（如本地Top50、美食节精选）
•	实拍评论、高质量点评提升内容信任度
•	用户可收藏、点赞、关注博主、升级称号系统
•	管理后台可管理商家、审核内容、编辑榜单
•	餐厅具备“等级”与“荣誉徽章”，如“本地人气餐厅”“高评分精选”等
________________________________________
后续拓展功能（V2）
•	商户自助入驻 + 广告投放系统
•	优惠券系统 + 到店核销机制
•	推荐算法（根据浏览记录 / 喜好）
•	美食专题栏目（节日活动 / Top 榜单）
•	API 接口开放合作（旅游社 / 内容平台）
•	App 小程序版本同步开发（React Native / Flutter）
•	后台徽章配置界面（支持设定条件与手动发放）
•	餐厅等级排行榜 + 勋章墙展示页（运营专题页）
