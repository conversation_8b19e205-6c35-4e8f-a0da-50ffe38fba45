@echo off
echo ========================================
echo    EatMY - Local Development Server
echo ========================================
echo.
echo Starting local HTTP server...
echo.
echo The website will be available at:
echo http://localhost:8000
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

REM Try Python 3 first
python -m http.server 8000 2>nul
if %errorlevel% neq 0 (
    REM If Python 3 fails, try Python 2
    python -m SimpleHTTPServer 8000 2>nul
    if %errorlevel% neq 0 (
        echo Error: Python is not installed or not in PATH
        echo.
        echo Please install Python from https://python.org
        echo Or use any other HTTP server like:
        echo - Node.js: npx http-server
        echo - PHP: php -S localhost:8000
        echo.
        pause
    )
)
