<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restaurant Details - EatMY</title>
    <meta name="description" content="View restaurant details, reviews, menu, and location information.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="index.html" class="nav-brand">EatMY</a>
            
            <ul class="nav-links">
                <li><a href="index.html" class="nav-link" data-translate="home">Home</a></li>
                <li><a href="search.html" class="nav-link" data-translate="search">Search</a></li>
                <li><a href="rankings.html" class="nav-link" data-translate="rankings">Rankings</a></li>
                <li><a href="categories.html" class="nav-link" data-translate="categories">Categories</a></li>
                <li><a href="map.html" class="nav-link" data-translate="map">Map</a></li>
            </ul>
            
            <div class="nav-actions">
                <!-- Language Switcher -->
                <div class="language-switcher">
                    <button class="language-button">
                        <span>EN</span>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                    </button>
                    <div class="language-dropdown">
                        <button class="language-option" data-language="en">English</button>
                        <button class="language-option" data-language="zh">中文</button>
                        <button class="language-option" data-language="ms">Bahasa</button>
                    </div>
                </div>
                
                <!-- User Menu -->
                <div class="user-menu" style="display: none;">
                    <a href="profile.html" class="profile-link btn btn-outline">Profile</a>
                    <button onclick="EatMY.logout()" class="btn btn-secondary" data-translate="logout">Logout</button>
                </div>
                
                <!-- Login Button -->
                <a href="login.html" class="login-btn btn btn-primary" data-translate="login">Login</a>
            </div>
            
            <button class="nav-toggle">☰</button>
        </nav>
    </header>

    <!-- Main Content -->
    <main style="margin-top: var(--header-height);">
        <!-- Breadcrumb -->
        <section class="breadcrumb section-sm" style="background: var(--gray-50);">
            <div class="container">
                <nav style="font-size: var(--text-sm); color: var(--gray-600);">
                    <a href="index.html" style="color: var(--gray-600);">Home</a>
                    <span style="margin: 0 var(--space-2);">›</span>
                    <a href="search.html" style="color: var(--gray-600);">Restaurants</a>
                    <span style="margin: 0 var(--space-2);">›</span>
                    <span id="restaurant-breadcrumb">Restaurant</span>
                </nav>
            </div>
        </section>

        <!-- Restaurant Hero -->
        <section class="restaurant-hero-section section-sm">
            <div class="container">
                <div class="restaurant-hero" id="restaurant-hero">
                    <!-- Hero content will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Restaurant Info -->
        <section class="restaurant-info-section">
            <div class="container">
                <div class="restaurant-info-grid">
                    <!-- Main Info -->
                    <div class="restaurant-main-info">
                        <!-- Description -->
                        <div class="restaurant-description" id="restaurant-description">
                            <!-- Description will be populated by JavaScript -->
                        </div>

                        <!-- Specialties -->
                        <div class="restaurant-specialties" id="restaurant-specialties" style="margin-top: var(--space-6);">
                            <!-- Specialties will be populated by JavaScript -->
                        </div>

                        <!-- Image Gallery -->
                        <div class="restaurant-gallery" id="restaurant-gallery" style="margin-top: var(--space-8);">
                            <h3 style="margin-bottom: var(--space-4);">Photos</h3>
                            <div class="gallery-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--space-4);">
                                <!-- Gallery images will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="restaurant-sidebar">
                        <!-- Quick Info -->
                        <div class="info-card" id="quick-info">
                            <h3 class="info-card-title">Quick Info</h3>
                            <!-- Quick info will be populated by JavaScript -->
                        </div>

                        <!-- Rating Breakdown -->
                        <div class="info-card" id="rating-breakdown">
                            <h3 class="info-card-title">Rating Breakdown</h3>
                            <!-- Rating breakdown will be populated by JavaScript -->
                        </div>

                        <!-- Opening Hours -->
                        <div class="info-card" id="opening-hours">
                            <h3 class="info-card-title">Opening Hours</h3>
                            <!-- Opening hours will be populated by JavaScript -->
                        </div>

                        <!-- Location -->
                        <div class="info-card" id="location-info">
                            <h3 class="info-card-title">Location</h3>
                            <!-- Location info will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Reviews Section -->
        <section class="reviews-section-container section">
            <div class="container">
                <div class="reviews-section" id="reviews-section">
                    <div class="reviews-header">
                        <h2 class="reviews-title" data-translate="reviews">Reviews</h2>
                        <a href="review.html" class="btn btn-primary" data-translate="writeReview">Write Review</a>
                    </div>

                    <!-- Review Summary -->
                    <div class="review-summary" id="review-summary" style="margin-bottom: var(--space-8); padding: var(--space-6); background: var(--gray-50); border-radius: var(--radius-xl);">
                        <!-- Review summary will be populated by JavaScript -->
                    </div>

                    <!-- Review Filters -->
                    <div class="review-filters" style="margin-bottom: var(--space-6);">
                        <div style="display: flex; gap: var(--space-4); align-items: center; flex-wrap: wrap;">
                            <span style="font-weight: 500; color: var(--gray-700);">Filter by:</span>
                            <select class="form-select" id="review-rating-filter" style="width: auto;">
                                <option value="all">All Ratings</option>
                                <option value="5">5 Stars</option>
                                <option value="4">4 Stars</option>
                                <option value="3">3 Stars</option>
                                <option value="2">2 Stars</option>
                                <option value="1">1 Star</option>
                            </select>
                            <select class="form-select" id="review-sort" style="width: auto;">
                                <option value="newest">Newest First</option>
                                <option value="oldest">Oldest First</option>
                                <option value="highest">Highest Rated</option>
                                <option value="lowest">Lowest Rated</option>
                                <option value="helpful">Most Helpful</option>
                            </select>
                        </div>
                    </div>

                    <!-- Reviews List -->
                    <div class="reviews-list" id="reviews-list">
                        <!-- Reviews will be populated by JavaScript -->
                    </div>

                    <!-- Load More Reviews -->
                    <div class="text-center" style="margin-top: var(--space-8);">
                        <button class="btn btn-outline" id="load-more-reviews">Load More Reviews</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Similar Restaurants -->
        <section class="similar-restaurants section" style="background: var(--gray-50);">
            <div class="container">
                <h2 style="text-align: center; margin-bottom: var(--space-8);">Similar Restaurants</h2>
                <div class="similar-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="similar-restaurants">
                    <!-- Similar restaurants will be populated by JavaScript -->
                </div>
            </div>
        </section>
    </main>

    <!-- Image Modal -->
    <div class="modal-overlay" id="image-modal">
        <div class="modal" style="max-width: 90vw; max-height: 90vh;">
            <div class="modal-header">
                <h3 class="modal-title">Restaurant Photos</h3>
                <button class="modal-close" onclick="EatMY.closeModal()">&times;</button>
            </div>
            <div class="modal-body" style="padding: 0;">
                <img id="modal-image" style="width: 100%; height: auto; display: block;">
            </div>
        </div>
    </div>

    <!-- Share Modal -->
    <div class="modal-overlay" id="share-modal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">Share Restaurant</h3>
                <button class="modal-close" onclick="EatMY.closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div style="display: flex; flex-direction: column; gap: var(--space-4);">
                    <button class="social-btn">
                        <span>📱</span>
                        Copy Link
                    </button>
                    <button class="social-btn">
                        <span>📘</span>
                        Share on Facebook
                    </button>
                    <button class="social-btn">
                        <span>🐦</span>
                        Share on Twitter
                    </button>
                    <button class="social-btn">
                        <span>📷</span>
                        Share on Instagram
                    </button>
                    <button class="social-btn">
                        <span>💬</span>
                        Share on WhatsApp
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script type="module" src="js/main.js"></script>
    <script type="module" src="js/restaurant-detail.js"></script>
</body>
</html>
