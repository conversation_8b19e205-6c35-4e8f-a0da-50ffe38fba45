// EatMY - Authentication Functionality

import { users } from './data.js';

// Initialize auth page
document.addEventListener('DOMContentLoaded', function() {
  initializeAuthPage();
});

function initializeAuthPage() {
  // Check if user is already logged in
  if (window.EatMY.currentUser) {
    window.location.href = 'profile.html';
    return;
  }
  
  // Initialize form handlers
  initializeLoginForm();
  initializeRegisterForm();
  initializeForgotPasswordForm();
  
  console.log('Auth page initialized');
}

function initializeLoginForm() {
  const loginForm = document.getElementById('login-form-email');
  if (loginForm) {
    loginForm.addEventListener('submit', handleLogin);
  }
}

function initializeRegisterForm() {
  const registerForm = document.getElementById('register-form-email');
  if (registerForm) {
    registerForm.addEventListener('submit', handleRegister);
  }
  
  // Password confirmation validation
  const passwordInput = document.getElementById('register-password');
  const confirmInput = document.getElementById('register-password-confirm');
  
  if (passwordInput && confirmInput) {
    confirmInput.addEventListener('input', function() {
      if (this.value !== passwordInput.value) {
        this.setCustomValidity('Passwords do not match');
      } else {
        this.setCustomValidity('');
      }
    });
  }
}

function initializeForgotPasswordForm() {
  const forgotForm = document.getElementById('forgot-password-form-email');
  if (forgotForm) {
    forgotForm.addEventListener('submit', handleForgotPassword);
  }
}

// Handle login form submission
function handleLogin(e) {
  e.preventDefault();
  
  const formData = new FormData(e.target);
  const email = formData.get('email');
  const password = formData.get('password');
  const remember = formData.get('remember');
  
  // Show loading state
  const submitBtn = e.target.querySelector('button[type="submit"]');
  const originalText = submitBtn.textContent;
  submitBtn.textContent = 'Signing in...';
  submitBtn.disabled = true;
  
  // Simulate API call
  setTimeout(() => {
    // Find user by email (in real app, this would be server-side)
    const user = users.find(u => u.email.toLowerCase() === email.toLowerCase());
    
    if (user && password === 'password123') { // Simple password check for demo
      // Successful login
      const userData = {
        id: user.id,
        name: user.name,
        email: user.email,
        avatar: user.avatar,
        level: user.level,
        reviewCount: user.reviewCount,
        followers: user.followers,
        following: user.following,
        favorites: user.favorites,
        joinDate: user.joinDate
      };
      
      // Save user session
      window.EatMY.currentUser = userData;
      localStorage.setItem('eatmy_user', JSON.stringify(userData));
      
      // Show success message
      showSuccessMessage('Welcome back! You have been successfully signed in.');
      
    } else {
      // Failed login
      showError('Invalid email or password. Please try again.');
      submitBtn.textContent = originalText;
      submitBtn.disabled = false;
    }
  }, 1000);
}

// Handle register form submission
function handleRegister(e) {
  e.preventDefault();
  
  const formData = new FormData(e.target);
  const name = formData.get('name');
  const email = formData.get('email');
  const password = formData.get('password');
  const passwordConfirm = formData.get('passwordConfirm');
  const acceptTerms = formData.get('terms');
  
  // Validation
  if (password !== passwordConfirm) {
    showError('Passwords do not match.');
    return;
  }
  
  if (!acceptTerms) {
    showError('Please accept the Terms of Service and Privacy Policy.');
    return;
  }
  
  // Check if email already exists
  const existingUser = users.find(u => u.email.toLowerCase() === email.toLowerCase());
  if (existingUser) {
    showError('An account with this email already exists.');
    return;
  }
  
  // Show loading state
  const submitBtn = e.target.querySelector('button[type="submit"]');
  const originalText = submitBtn.textContent;
  submitBtn.textContent = 'Creating account...';
  submitBtn.disabled = true;
  
  // Simulate API call
  setTimeout(() => {
    // Create new user
    const newUser = {
      id: users.length + 1,
      name: name,
      email: email,
      avatar: `https://images.unsplash.com/photo-*************-5658abf4ff4e?w=100`, // Default avatar
      level: 'New Foodie',
      reviewCount: 0,
      followers: 0,
      following: 0,
      favorites: [],
      joinDate: new Date().toISOString().split('T')[0]
    };
    
    // In a real app, this would be saved to the server
    users.push(newUser);
    
    // Auto-login the new user
    window.EatMY.currentUser = newUser;
    localStorage.setItem('eatmy_user', JSON.stringify(newUser));
    
    // Show success message
    showSuccessMessage('Welcome to EatMY! Your account has been created successfully.');
    
  }, 1500);
}

// Handle forgot password form submission
function handleForgotPassword(e) {
  e.preventDefault();
  
  const formData = new FormData(e.target);
  const email = formData.get('email');
  
  // Show loading state
  const submitBtn = e.target.querySelector('button[type="submit"]');
  const originalText = submitBtn.textContent;
  submitBtn.textContent = 'Sending...';
  submitBtn.disabled = true;
  
  // Simulate API call
  setTimeout(() => {
    // Check if email exists
    const user = users.find(u => u.email.toLowerCase() === email.toLowerCase());
    
    if (user) {
      showSuccessMessage('Password reset instructions have been sent to your email.');
    } else {
      showError('No account found with this email address.');
      submitBtn.textContent = originalText;
      submitBtn.disabled = false;
    }
  }, 1000);
}

// Social login handler
function socialLogin(provider) {
  // Show loading notification
  window.EatMY.showNotification(`Connecting to ${provider}...`, 'info');
  
  // Simulate social login
  setTimeout(() => {
    // For demo purposes, create a mock social user
    const socialUser = {
      id: 999,
      name: provider === 'google' ? 'John Doe' : 'Jane Smith',
      email: provider === 'google' ? '<EMAIL>' : '<EMAIL>',
      avatar: provider === 'google' ? 
        'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=100' :
        'https://images.unsplash.com/photo-*************-2616b612b786?w=100',
      level: 'Food Explorer',
      reviewCount: 12,
      followers: 45,
      following: 23,
      favorites: [1, 3, 5],
      joinDate: '2023-01-15'
    };
    
    // Save user session
    window.EatMY.currentUser = socialUser;
    localStorage.setItem('eatmy_user', JSON.stringify(socialUser));
    
    // Show success message
    showSuccessMessage(`Successfully signed in with ${provider}!`);
    
  }, 2000);
}

// Form switching functions
function showLoginForm() {
  document.getElementById('login-form').style.display = 'block';
  document.getElementById('register-form').style.display = 'none';
  document.getElementById('forgot-password-form').style.display = 'none';
  
  // Clear forms
  clearForms();
}

function showRegisterForm() {
  document.getElementById('login-form').style.display = 'none';
  document.getElementById('register-form').style.display = 'block';
  document.getElementById('forgot-password-form').style.display = 'none';
  
  // Clear forms
  clearForms();
}

function showForgotPassword() {
  document.getElementById('login-form').style.display = 'none';
  document.getElementById('register-form').style.display = 'none';
  document.getElementById('forgot-password-form').style.display = 'block';
  
  // Clear forms
  clearForms();
}

function clearForms() {
  // Clear all form inputs
  const forms = document.querySelectorAll('.auth-form');
  forms.forEach(form => {
    form.reset();
    
    // Reset button states
    const submitBtn = form.querySelector('button[type="submit"]');
    if (submitBtn) {
      submitBtn.disabled = false;
      
      // Reset button text based on form
      if (form.id === 'login-form-email') {
        submitBtn.textContent = 'Sign In';
      } else if (form.id === 'register-form-email') {
        submitBtn.textContent = 'Create Account';
      } else if (form.id === 'forgot-password-form-email') {
        submitBtn.textContent = 'Send Reset Link';
      }
    }
  });
  
  // Clear any error messages
  clearErrors();
}

function showError(message) {
  // Remove existing error messages
  clearErrors();
  
  // Create error message element
  const errorDiv = document.createElement('div');
  errorDiv.className = 'error-message';
  errorDiv.style.cssText = `
    background: var(--error);
    color: white;
    padding: var(--space-3);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-4);
    font-size: var(--text-sm);
  `;
  errorDiv.textContent = message;
  
  // Insert error message at the top of the visible form
  const visibleForm = document.querySelector('.auth-form:not([style*="display: none"])') ||
                     document.querySelector('.auth-form');
  
  if (visibleForm) {
    visibleForm.insertBefore(errorDiv, visibleForm.firstChild);
  }
}

function clearErrors() {
  const errorMessages = document.querySelectorAll('.error-message');
  errorMessages.forEach(error => error.remove());
}

function showSuccessMessage(message) {
  const successMessageElement = document.getElementById('success-message');
  if (successMessageElement) {
    successMessageElement.textContent = message;
  }
  
  window.EatMY.openModal('success-modal');
}

function redirectAfterLogin() {
  window.EatMY.closeModal();
  
  // Check if there's a redirect URL in the query params
  const urlParams = new URLSearchParams(window.location.search);
  const redirect = urlParams.get('redirect');
  
  if (redirect) {
    window.location.href = decodeURIComponent(redirect);
  } else {
    window.location.href = 'profile.html';
  }
}

// Demo login function for testing
function demoLogin() {
  const demoUser = users[0]; // Use first user from data
  
  window.EatMY.currentUser = demoUser;
  localStorage.setItem('eatmy_user', JSON.stringify(demoUser));
  
  showSuccessMessage('Demo login successful!');
}

// Make functions available globally
window.showLoginForm = showLoginForm;
window.showRegisterForm = showRegisterForm;
window.showForgotPassword = showForgotPassword;
window.socialLogin = socialLogin;
window.redirectAfterLogin = redirectAfterLogin;
window.demoLogin = demoLogin;

// Add demo login button for testing (only in development)
document.addEventListener('DOMContentLoaded', function() {
  // Add demo login option
  setTimeout(() => {
    const authCard = document.querySelector('.auth-card');
    if (authCard) {
      const demoButton = document.createElement('div');
      demoButton.style.cssText = `
        text-align: center;
        margin-top: var(--space-4);
        padding-top: var(--space-4);
        border-top: 1px solid var(--gray-200);
      `;
      demoButton.innerHTML = `
        <p style="font-size: var(--text-sm); color: var(--gray-600); margin-bottom: var(--space-2);">
          For demo purposes:
        </p>
        <button onclick="demoLogin()" class="btn btn-outline" style="font-size: var(--text-sm);">
          Demo Login
        </button>
      `;
      authCard.appendChild(demoButton);
    }
  }, 100);
});
