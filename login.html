<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - EatMY</title>
    <meta name="description" content="Login to your EatMY account to write reviews, save favorites, and connect with the food community.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="index.html" class="nav-brand">EatMY</a>
            
            <ul class="nav-links">
                <li><a href="index.html" class="nav-link" data-translate="home">Home</a></li>
                <li><a href="search.html" class="nav-link" data-translate="search">Search</a></li>
                <li><a href="rankings.html" class="nav-link" data-translate="rankings">Rankings</a></li>
                <li><a href="categories.html" class="nav-link" data-translate="categories">Categories</a></li>
                <li><a href="map.html" class="nav-link" data-translate="map">Map</a></li>
            </ul>
            
            <div class="nav-actions">
                <!-- Language Switcher -->
                <div class="language-switcher">
                    <button class="language-button">
                        <span>EN</span>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                    </button>
                    <div class="language-dropdown">
                        <button class="language-option" data-language="en">English</button>
                        <button class="language-option" data-language="zh">中文</button>
                        <button class="language-option" data-language="ms">Bahasa</button>
                    </div>
                </div>
                
                <a href="index.html" class="btn btn-outline">Back to Home</a>
            </div>
            
            <button class="nav-toggle">☰</button>
        </nav>
    </header>

    <!-- Main Content -->
    <main style="margin-top: var(--header-height);">
        <div class="auth-container">
            <div class="auth-card">
                <!-- Login Form -->
                <div class="login-form-container" id="login-form">
                    <h1 class="auth-title">Welcome Back</h1>
                    <p class="auth-subtitle">Sign in to your EatMY account</p>
                    
                    <!-- Social Login -->
                    <div class="social-login">
                        <button class="social-btn" onclick="socialLogin('google')">
                            <span>🔍</span>
                            Continue with Google
                        </button>
                        <button class="social-btn" onclick="socialLogin('facebook')">
                            <span>📘</span>
                            Continue with Facebook
                        </button>
                    </div>
                    
                    <div class="auth-divider">
                        <span>or</span>
                    </div>
                    
                    <!-- Email Login Form -->
                    <form class="auth-form" id="login-form-email">
                        <div class="form-group">
                            <label class="form-label" for="login-email">Email Address</label>
                            <input 
                                type="email" 
                                class="form-input" 
                                id="login-email" 
                                name="email"
                                placeholder="Enter your email"
                                required
                            >
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="login-password">Password</label>
                            <input 
                                type="password" 
                                class="form-input" 
                                id="login-password" 
                                name="password"
                                placeholder="Enter your password"
                                required
                            >
                        </div>
                        
                        <div class="form-group" style="display: flex; justify-content: space-between; align-items: center;">
                            <label style="display: flex; align-items: center; gap: var(--space-2); cursor: pointer;">
                                <input type="checkbox" name="remember" id="remember-me">
                                <span style="font-size: var(--text-sm);">Remember me</span>
                            </label>
                            <a href="#" onclick="showForgotPassword()" style="font-size: var(--text-sm); color: var(--primary-color);">
                                Forgot password?
                            </a>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            Sign In
                        </button>
                    </form>
                    
                    <div class="auth-footer">
                        Don't have an account? 
                        <a href="#" onclick="showRegisterForm()">Sign up here</a>
                    </div>
                </div>
                
                <!-- Register Form -->
                <div class="register-form-container" id="register-form" style="display: none;">
                    <h1 class="auth-title">Join EatMY</h1>
                    <p class="auth-subtitle">Create your account to start exploring</p>
                    
                    <!-- Social Register -->
                    <div class="social-login">
                        <button class="social-btn" onclick="socialLogin('google')">
                            <span>🔍</span>
                            Sign up with Google
                        </button>
                        <button class="social-btn" onclick="socialLogin('facebook')">
                            <span>📘</span>
                            Sign up with Facebook
                        </button>
                    </div>
                    
                    <div class="auth-divider">
                        <span>or</span>
                    </div>
                    
                    <!-- Email Register Form -->
                    <form class="auth-form" id="register-form-email">
                        <div class="form-group">
                            <label class="form-label" for="register-name">Full Name</label>
                            <input 
                                type="text" 
                                class="form-input" 
                                id="register-name" 
                                name="name"
                                placeholder="Enter your full name"
                                required
                            >
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="register-email">Email Address</label>
                            <input 
                                type="email" 
                                class="form-input" 
                                id="register-email" 
                                name="email"
                                placeholder="Enter your email"
                                required
                            >
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="register-password">Password</label>
                            <input 
                                type="password" 
                                class="form-input" 
                                id="register-password" 
                                name="password"
                                placeholder="Create a password"
                                required
                                minlength="6"
                            >
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="register-password-confirm">Confirm Password</label>
                            <input 
                                type="password" 
                                class="form-input" 
                                id="register-password-confirm" 
                                name="passwordConfirm"
                                placeholder="Confirm your password"
                                required
                            >
                        </div>
                        
                        <div class="form-group">
                            <label style="display: flex; align-items: start; gap: var(--space-2); cursor: pointer; font-size: var(--text-sm);">
                                <input type="checkbox" name="terms" id="accept-terms" required style="margin-top: 2px;">
                                <span>
                                    I agree to the <a href="#" style="color: var(--primary-color);">Terms of Service</a> 
                                    and <a href="#" style="color: var(--primary-color);">Privacy Policy</a>
                                </span>
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            Create Account
                        </button>
                    </form>
                    
                    <div class="auth-footer">
                        Already have an account? 
                        <a href="#" onclick="showLoginForm()">Sign in here</a>
                    </div>
                </div>
                
                <!-- Forgot Password Form -->
                <div class="forgot-password-container" id="forgot-password-form" style="display: none;">
                    <h1 class="auth-title">Reset Password</h1>
                    <p class="auth-subtitle">Enter your email to receive reset instructions</p>
                    
                    <form class="auth-form" id="forgot-password-form-email">
                        <div class="form-group">
                            <label class="form-label" for="forgot-email">Email Address</label>
                            <input 
                                type="email" 
                                class="form-input" 
                                id="forgot-email" 
                                name="email"
                                placeholder="Enter your email"
                                required
                            >
                        </div>
                        
                        <button type="submit" class="btn btn-primary" style="width: 100%;">
                            Send Reset Link
                        </button>
                    </form>
                    
                    <div class="auth-footer">
                        Remember your password? 
                        <a href="#" onclick="showLoginForm()">Sign in here</a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Success Modal -->
    <div class="modal-overlay" id="success-modal">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">Success!</h3>
                <button class="modal-close" onclick="EatMY.closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div style="text-align: center;">
                    <div style="font-size: 48px; margin-bottom: var(--space-4);">✅</div>
                    <p id="success-message">Welcome to EatMY!</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="redirectAfterLogin()">Continue</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script type="module" src="js/main.js"></script>
    <script type="module" src="js/auth.js"></script>
</body>
</html>
