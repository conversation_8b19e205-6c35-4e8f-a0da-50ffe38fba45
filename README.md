# EatMY - Malaysian Food Review Platform

## 🍽️ Project Overview

**EatMY** is a Malaysian localized restaurant review and food recommendation platform designed to connect food enthusiasts, international tourists, and local restaurants. The platform combines community-driven content with merchant growth tools and multi-language local experiences.

### Target Users
- Malaysian young people and food enthusiasts
- International tourists visiting Malaysia
- Small and medium-sized food businesses

### Core Value Proposition
"Malaysia's most influential local dining review ecosystem platform, balancing content community with merchant growth."

## 🎯 Features & Pages

### Core Pages
1. **Homepage** (`index.html`) - Featured restaurants, search entry, recommendations
2. **Search Page** (`search.html`) - Advanced filters, results display
3. **Rankings Page** (`rankings.html`) - Top picks, Top 100 restaurants, festival recommendations
4. **Restaurant Detail** (`restaurant-detail.html`) - Ratings, reviews, menu, map integration
5. **User Authentication** (`login.html`) - Login/registration with social options
6. **User Profile** (`profile.html`) - My reviews, favorites, following
7. **Map Exploration** (`map.html`) - Interactive map with restaurant markers
8. **Write Review** (`review.html`) - Multi-dimensional rating system

### Additional Pages
9. **Categories** (`categories.html`) - Browse by cuisine type (inspired by Tabelog)
10. **All Restaurants** (`restaurants.html`) - Complete restaurant listing with pagination
11. **About** (`about.html`) - Platform information
12. **Help** (`help.html`) - FAQ and support

## 🏗️ Technical Architecture

### File Structure
```
/
├── README.md
├── index.html                 # Homepage
├── search.html               # Search & filters
├── rankings.html             # Top restaurants
├── restaurant-detail.html    # Restaurant details
├── login.html               # Authentication
├── profile.html             # User profile
├── map.html                 # Map exploration
├── review.html              # Write reviews
├── categories.html          # Cuisine categories
├── restaurants.html         # All restaurants
├── about.html               # About page
├── help.html                # Help & FAQ
├── css/
│   ├── main.css             # Global styles & variables
│   ├── components.css       # Reusable components
│   └── pages.css            # Page-specific styles
├── js/
│   ├── main.js              # Core application logic
│   ├── data.js              # Mock data & API simulation
│   ├── components.js        # Reusable UI components
│   ├── router.js            # Client-side routing
│   └── utils.js             # Utility functions
├── assets/
│   ├── images/              # Restaurant & UI images
│   └── icons/               # Icon set
└── data/
    └── restaurants.json     # Mock restaurant data
```

### Technology Stack
- **HTML5** - Semantic markup structure
- **CSS3** - Grid, Flexbox, Custom Properties, Animations
- **Vanilla JavaScript** - ES6+ modules, async/await
- **Local Storage** - Data persistence
- **Responsive Design** - Mobile-first approach

## 🎨 Design System

### Design Inspiration
- **Notion** - Clean, minimal interface with excellent typography
- **Apple** - Refined aesthetics, smooth interactions, attention to detail
- **Tabelog** - Category organization and restaurant classification

### Visual Identity
- **Colors**: Clean whites, subtle grays, warm accent colors (orange/red for food theme)
- **Typography**: Clean sans-serif fonts with clear hierarchy
- **Components**: Cards with subtle shadows, rounded corners, clean buttons
- **Layout**: Generous white space, grid-based layouts
- **Icons**: Simple, consistent icon set

### Malaysian Localization
- **Cuisine Categories**: Malay, Chinese, Indian, Western, Fusion
- **Halal Certification**: Clear indicators for Muslim users
- **Multi-language**: English, Chinese, Bahasa Malaysia
- **Local Areas**: KL, Penang, Johor Bahru, Ipoh, etc.
- **Currency**: Malaysian Ringgit (MYR)

## 🔧 Core Features Implementation

### 1. Restaurant Rating System
- **Multi-dimensional ratings**: Taste, Environment, Service
- **Overall score calculation**: Weighted average
- **Visual representation**: Star ratings with half-star precision

### 2. Search & Filtering
- **Text search**: Restaurant name, cuisine, location
- **Filters**: Cuisine type, price range, ratings, Halal status
- **Sorting**: Rating, distance, price, newest reviews

### 3. User Review System
- **Rich content**: Text + image uploads
- **Rating dimensions**: Taste (40%), Environment (30%), Service (30%)
- **Social features**: Like, reply, report
- **User levels**: Based on review count and quality

### 4. Restaurant Classification
- **Categories**: Primary cuisine type
- **Tags**: Dietary restrictions, atmosphere, price range
- **Badges**: High rating, popular, Halal certified, etc.
- **Levels**: Based on review count, rating, and engagement

### 5. Interactive Features
- **Image galleries**: Swipeable restaurant photos
- **Map integration**: Location display and directions
- **Favorites system**: Save restaurants to collections
- **Social interactions**: Follow users, like reviews

## 📱 Responsive Design

### Breakpoints
- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px+

### Mobile-First Features
- Touch-friendly interface
- Swipe gestures for galleries
- Collapsible navigation
- Optimized image loading

## 🚀 Development Phases

### Phase 1: Foundation ✅ COMPLETED
- [x] Project planning and documentation
- [x] Basic file structure setup
- [x] Global styles and design system
- [x] Navigation component

### Phase 2: Core Pages ✅ COMPLETED
- [x] Homepage with featured content
- [x] Restaurant listing and detail pages
- [x] Search functionality with filters
- [x] Basic user authentication
- [x] Categories page with cuisine browsing
- [x] Rankings page with multiple ranking types

### Phase 3: Interactive Features ✅ COMPLETED
- [x] Review system with multi-dimensional ratings
- [x] User profiles with favorites and settings
- [x] Favorite restaurants functionality
- [x] Social features (likes, follows simulation)
- [x] Multi-language support (EN/ZH/MS)

### Phase 4: Polish & Optimization ✅ COMPLETED
- [x] Responsive design for mobile/tablet/desktop
- [x] Performance optimization with lazy loading
- [x] Cross-browser compatibility
- [x] Accessibility improvements
- [x] Interactive animations and transitions

## 🧪 Testing Strategy

### Manual Testing
- Cross-browser compatibility (Chrome, Firefox, Safari, Edge)
- Responsive design on various devices
- User flow testing for all major features
- Performance testing on slower connections

### Accessibility
- Semantic HTML structure
- ARIA labels for interactive elements
- Keyboard navigation support
- Color contrast compliance

## 📈 Future Enhancements (V2)

- Merchant self-service portal
- Advanced recommendation algorithm
- Mobile app development
- API for third-party integrations
- Advanced analytics dashboard
- Coupon and promotion system

## 🛠️ Development Setup

1. Clone or download the project files
2. Open `index.html` in a modern web browser
3. For development, use a local server (e.g., Live Server extension in VS Code)
4. All data is mocked and stored locally - no backend required

## 📝 Usage Instructions

### For Users
- **Browse restaurants** on the homepage with featured content and categories
- **Search & filter** restaurants by cuisine, location, rating, and dietary preferences
- **Read detailed reviews** with multi-dimensional ratings (taste, environment, service)
- **Write reviews** with photo uploads and star ratings
- **Save favorites** to your personal collection
- **Explore rankings** across different categories (overall, budget, luxury, etc.)
- **User profiles** to track your reviews, favorites, and activity
- **Multi-language support** - switch between English, Chinese, and Bahasa Malaysia

### For Development
- **Mock data**: Modify `js/data.js` to add new restaurants, categories, or users
- **Styling**: Update CSS files following the design system variables
- **New pages**: Create HTML files and update navigation in `js/main.js`
- **Features**: Extend functionality in the modular JavaScript files
- **Testing**: Use demo login feature for quick testing
- **Responsive**: All pages are mobile-first and responsive

### Demo Features
- **Demo login** available on login page for quick testing
- **Sample data** includes 5 restaurants with reviews and user profiles
- **Interactive features** like favorites, search, and filtering work with local storage
- **Realistic UI** with proper loading states, animations, and feedback

### File Structure Overview
```
├── index.html              # Homepage with featured restaurants
├── search.html             # Search with advanced filters
├── restaurant-detail.html  # Individual restaurant pages
├── rankings.html           # Restaurant rankings by category
├── categories.html         # Browse by cuisine type
├── login.html              # Authentication (demo available)
├── profile.html            # User profiles and settings
├── about.html              # About the platform
├── css/                    # Modular CSS architecture
├── js/                     # JavaScript modules
└── README.md               # This documentation
```

---

*This prototype demonstrates the complete functionality of the EatMY platform using frontend technologies only. All data is simulated and stored locally - no backend integration is required for testing.*
