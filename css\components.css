/* EatMY - Reusable Components */

/* Header & Navigation */
.header {
  background: var(--white);
  border-bottom: 1px solid var(--gray-200);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: var(--header-height);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 var(--space-4);
}

.nav-brand {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: var(--space-6);
  margin: 0;
}

.nav-link {
  color: var(--gray-700);
  font-weight: 500;
  transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

/* Mobile Navigation */
.nav-toggle {
  display: none;
  background: none;
  border: none;
  font-size: var(--text-xl);
  cursor: pointer;
  color: var(--gray-700);
}

@media (max-width: 768px) {
  .nav-links {
    position: fixed;
    top: var(--header-height);
    left: 0;
    right: 0;
    background: var(--white);
    flex-direction: column;
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
    transform: translateY(-100%);
    transition: transform var(--transition-normal);
    opacity: 0;
    visibility: hidden;
  }
  
  .nav-links.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }
  
  .nav-toggle {
    display: block;
  }
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background: var(--primary-dark);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--white);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
  background: var(--gray-50);
  border-color: var(--gray-400);
  color: var(--gray-800);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: var(--white);
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
}

/* Cards */
.card {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}

/* Restaurant Cards */
.restaurant-card {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.restaurant-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

.restaurant-card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  background: var(--gray-200);
}

.restaurant-card-content {
  padding: var(--space-4);
}

.restaurant-card-title {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-2);
  color: var(--gray-900);
}

.restaurant-card-cuisine {
  color: var(--gray-600);
  font-size: var(--text-sm);
  margin-bottom: var(--space-3);
}

.restaurant-card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Rating Component */
.rating {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.rating-stars {
  display: flex;
  gap: var(--space-1);
}

.rating-star {
  color: var(--gray-300);
  font-size: var(--text-sm);
}

.rating-star.filled {
  color: var(--accent-color);
}

.rating-value {
  font-weight: 600;
  color: var(--gray-800);
}

.rating-count {
  color: var(--gray-500);
  font-size: var(--text-sm);
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background: var(--primary-color);
  color: var(--white);
}

.badge-success {
  background: var(--success);
  color: var(--white);
}

.badge-warning {
  background: var(--warning);
  color: var(--white);
}

.badge-halal {
  background: var(--success);
  color: var(--white);
}

.badge-popular {
  background: var(--accent-color);
  color: var(--white);
}

/* Form Elements */
.form-group {
  margin-bottom: var(--space-4);
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 500;
  color: var(--gray-700);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  transition: border-color var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(255 107 53 / 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

/* Search Bar */
.search-bar {
  position: relative;
  max-width: 500px;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: var(--space-4) var(--space-12) var(--space-4) var(--space-4);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-xl);
  font-size: var(--text-base);
  background: var(--white);
  box-shadow: var(--shadow-sm);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.search-button {
  position: absolute;
  right: var(--space-2);
  top: 50%;
  transform: translateY(-50%);
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-2);
  cursor: pointer;
  transition: background var(--transition-fast);
}

.search-button:hover {
  background: var(--primary-dark);
}

/* Language Switcher */
.language-switcher {
  position: relative;
}

.language-button {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--white);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: var(--text-sm);
}

.language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  min-width: 120px;
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-fast);
}

.language-dropdown.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.language-option {
  display: block;
  width: 100%;
  padding: var(--space-3);
  text-align: left;
  border: none;
  background: none;
  cursor: pointer;
  font-size: var(--text-sm);
  transition: background var(--transition-fast);
}

.language-option:hover {
  background: var(--gray-50);
}

/* Loading Spinner */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-300);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal {
  background: var(--white);
  border-radius: var(--radius-xl);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.9);
  transition: transform var(--transition-normal);
}

.modal-overlay.active .modal {
  transform: scale(1);
}

.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: var(--text-xl);
  font-weight: 600;
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--text-xl);
  cursor: pointer;
  color: var(--gray-500);
  padding: var(--space-2);
}

.modal-body {
  padding: var(--space-6);
}

.modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end);
}

/* Homepage Specific Styles */
.hero {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  padding: var(--space-20) 0;
}

.hero-title {
  font-size: var(--text-4xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
  color: var(--white);
}

.hero-subtitle {
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin: 0 auto;
}

.category-quick {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  text-decoration: none;
  color: var(--white);
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
}

.category-quick:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  color: var(--white);
}

.category-icon {
  font-size: var(--text-2xl);
}

.category-name {
  font-size: var(--text-sm);
  font-weight: 500;
}

.area-card {
  display: block;
  padding: var(--space-4);
  background: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  text-decoration: none;
  color: var(--gray-800);
  transition: all var(--transition-fast);
  text-align: center;
}

.area-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  color: var(--gray-800);
}

.footer {
  background: var(--gray-900);
  color: var(--white);
}

.footer-title {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--space-4);
}

.footer-heading {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--space-4);
  color: var(--white);
}

.footer-links {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: var(--space-2);
}

.footer-links a {
  color: var(--gray-400);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--white);
}

.social-links {
  display: flex;
  gap: var(--space-4);
}

.social-link {
  color: var(--gray-400);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.social-link:hover {
  color: var(--primary-color);
}
