// EatMY - Homepage Functionality

import { restaurants, categories, getRestaurantsByCategory } from './data.js';

// Initialize homepage when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  initializeHomepage();
});

function initializeHomepage() {
  loadFeaturedRestaurants();
  loadTopRatedRestaurants();
  loadCategories();
  initializeFavoriteButtons();
  
  console.log('Homepage initialized successfully');
}

// Load featured restaurants
function loadFeaturedRestaurants() {
  const container = document.querySelector('.restaurants-grid');
  if (!container) return;
  
  // Get first 6 restaurants as featured
  const featuredRestaurants = restaurants.slice(0, 6);
  
  container.innerHTML = featuredRestaurants.map(restaurant => 
    createRestaurantCard(restaurant)
  ).join('');
  
  // Initialize favorite buttons for these cards
  initializeFavoriteButtons();
}

// Load top rated restaurants
function loadTopRatedRestaurants() {
  const container = document.querySelector('.top-rated-grid');
  if (!container) return;
  
  // Sort restaurants by rating and get top 4
  const topRated = [...restaurants]
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 4);
  
  container.innerHTML = topRated.map(restaurant => 
    createCompactRestaurantCard(restaurant)
  ).join('');
}

// Load categories
function loadCategories() {
  const container = document.querySelector('.categories-grid');
  if (!container) return;
  
  container.innerHTML = categories.map(category => 
    createCategoryCard(category)
  ).join('');
}

// Create restaurant card HTML
function createRestaurantCard(restaurant) {
  const isFavorite = window.EatMY.favorites.includes(restaurant.id);
  const badges = restaurant.badges.map(badge => 
    `<span class="badge badge-${badge}">${badge.replace('-', ' ')}</span>`
  ).join('');
  
  return `
    <div class="restaurant-card" onclick="window.location.href='restaurant-detail.html?id=${restaurant.id}'">
      <div class="restaurant-card-image-container" style="position: relative;">
        <img 
          src="${restaurant.image}" 
          alt="${restaurant.name}"
          class="restaurant-card-image"
          loading="lazy"
        >
        <button 
          class="favorite-btn" 
          data-restaurant-id="${restaurant.id}"
          onclick="event.stopPropagation(); window.EatMY.toggleFavorite(${restaurant.id})"
          style="position: absolute; top: 12px; right: 12px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 36px; height: 36px; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 16px;"
          ${isFavorite ? 'class="active"' : ''}
        >
          ${isFavorite ? '❤️' : '🤍'}
        </button>
        ${restaurant.isOpen ? 
          '<div class="status-badge open" style="position: absolute; bottom: 12px; left: 12px; background: var(--success); color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">Open Now</div>' : 
          '<div class="status-badge closed" style="position: absolute; bottom: 12px; left: 12px; background: var(--error); color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">Closed</div>'
        }
      </div>
      
      <div class="restaurant-card-content">
        <div class="restaurant-card-header" style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 8px;">
          <h3 class="restaurant-card-title">${restaurant.name}</h3>
          ${restaurant.isHalal ? '<span class="badge badge-halal">Halal</span>' : ''}
        </div>
        
        <p class="restaurant-card-cuisine">${getCuisineDisplayName(restaurant.cuisine)} • ${restaurant.area}</p>
        
        <div class="restaurant-card-badges" style="margin-bottom: 12px;">
          ${badges}
        </div>
        
        <div class="restaurant-card-footer">
          <div class="rating">
            <div class="rating-stars">
              ${createStarRating(restaurant.rating)}
            </div>
            <span class="rating-value">${restaurant.rating}</span>
            <span class="rating-count">(${restaurant.reviewCount})</span>
          </div>
          
          <div class="price-range text-sm text-gray-600">
            ${restaurant.priceRange}
          </div>
        </div>
      </div>
    </div>
  `;
}

// Create compact restaurant card for top rated section
function createCompactRestaurantCard(restaurant) {
  return `
    <div class="restaurant-card compact" onclick="window.location.href='restaurant-detail.html?id=${restaurant.id}'" style="cursor: pointer;">
      <img 
        src="${restaurant.image}" 
        alt="${restaurant.name}"
        class="restaurant-card-image"
        style="height: 120px;"
        loading="lazy"
      >
      
      <div class="restaurant-card-content" style="padding: 16px;">
        <h4 class="restaurant-card-title" style="font-size: 16px; margin-bottom: 4px;">${restaurant.name}</h4>
        <p class="restaurant-card-cuisine" style="font-size: 14px; margin-bottom: 8px;">${getCuisineDisplayName(restaurant.cuisine)}</p>
        
        <div class="rating" style="font-size: 14px;">
          <div class="rating-stars">
            ${createStarRating(restaurant.rating)}
          </div>
          <span class="rating-value">${restaurant.rating}</span>
        </div>
      </div>
    </div>
  `;
}

// Create category card
function createCategoryCard(category) {
  const restaurantCount = getRestaurantsByCategory(category.id).length;
  
  return `
    <a href="search.html?category=${category.id}" class="category-card" style="display: block; text-decoration: none;">
      <div class="card" style="text-align: center; padding: 24px; transition: all 0.2s ease;">
        <div class="category-icon" style="font-size: 48px; margin-bottom: 16px;">${category.icon}</div>
        <h3 class="category-name" style="font-size: 18px; font-weight: 600; margin-bottom: 8px; color: var(--gray-900);">${category.name}</h3>
        <p class="category-count" style="font-size: 14px; color: var(--gray-600); margin: 0;">${restaurantCount} restaurants</p>
      </div>
    </a>
  `;
}

// Create star rating HTML
function createStarRating(rating) {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  let stars = '';
  
  // Full stars
  for (let i = 0; i < fullStars; i++) {
    stars += '<span class="rating-star filled">★</span>';
  }
  
  // Half star
  if (hasHalfStar) {
    stars += '<span class="rating-star half">☆</span>';
  }
  
  // Empty stars
  for (let i = 0; i < emptyStars; i++) {
    stars += '<span class="rating-star">☆</span>';
  }
  
  return stars;
}

// Get cuisine display name
function getCuisineDisplayName(cuisine) {
  const cuisineMap = {
    'malay': 'Malay',
    'chinese': 'Chinese',
    'indian': 'Indian',
    'western': 'Western',
    'japanese': 'Japanese',
    'korean': 'Korean',
    'thai': 'Thai',
    'fusion': 'Fusion'
  };
  
  return cuisineMap[cuisine] || cuisine;
}

// Initialize favorite buttons
function initializeFavoriteButtons() {
  const favoriteButtons = document.querySelectorAll('.favorite-btn');
  
  favoriteButtons.forEach(button => {
    const restaurantId = parseInt(button.dataset.restaurantId);
    const isFavorite = window.EatMY.favorites.includes(restaurantId);
    
    button.classList.toggle('active', isFavorite);
    button.innerHTML = isFavorite ? '❤️' : '🤍';
    
    // Remove existing event listeners to prevent duplicates
    button.replaceWith(button.cloneNode(true));
  });
  
  // Re-attach event listeners
  document.querySelectorAll('.favorite-btn').forEach(button => {
    button.addEventListener('click', function(e) {
      e.stopPropagation();
      const restaurantId = parseInt(this.dataset.restaurantId);
      window.EatMY.toggleFavorite(restaurantId);
    });
  });
}

// Add hover effects to category cards
document.addEventListener('DOMContentLoaded', function() {
  // Add hover effects after a short delay to ensure elements are loaded
  setTimeout(() => {
    const categoryCards = document.querySelectorAll('.category-card .card');
    categoryCards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-4px)';
        this.style.boxShadow = 'var(--shadow-lg)';
      });
      
      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = 'var(--shadow-sm)';
      });
    });
  }, 100);
});

// Add loading animation
function showLoading(container) {
  if (container) {
    container.innerHTML = `
      <div style="display: flex; justify-content: center; align-items: center; height: 200px;">
        <div class="spinner"></div>
      </div>
    `;
  }
}

// Lazy loading for images
function initializeLazyLoading() {
  const images = document.querySelectorAll('img[loading="lazy"]');
  
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src || img.src;
          img.classList.remove('lazy');
          imageObserver.unobserve(img);
        }
      });
    });
    
    images.forEach(img => imageObserver.observe(img));
  }
}

// Initialize lazy loading when page loads
document.addEventListener('DOMContentLoaded', function() {
  setTimeout(initializeLazyLoading, 500);
});

// Export functions for testing
window.EatMYHome = {
  loadFeaturedRestaurants,
  loadTopRatedRestaurants,
  loadCategories,
  createRestaurantCard,
  createStarRating
};
