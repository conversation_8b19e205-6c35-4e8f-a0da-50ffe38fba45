// EatMY - Rankings Page Functionality

import { restaurants } from './data.js';

let currentRanking = 'overall';

// Initialize rankings page
document.addEventListener('DOMContentLoaded', function() {
  initializeRankingsPage();
});

function initializeRankingsPage() {
  initializeRankingTabs();
  loadRankingData(currentRanking);
  
  console.log('Rankings page initialized');
}

function initializeRankingTabs() {
  const rankingTabs = document.querySelectorAll('.ranking-tab');
  
  rankingTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const rankingType = this.dataset.ranking;
      switchRanking(rankingType);
    });
  });
}

function switchRanking(rankingType) {
  // Update active tab
  document.querySelectorAll('.ranking-tab').forEach(tab => {
    tab.classList.remove('active');
  });
  document.querySelector(`[data-ranking="${rankingType}"]`).classList.add('active');
  
  // Update active section
  document.querySelectorAll('.ranking-section').forEach(section => {
    section.style.display = 'none';
  });
  document.getElementById(`${rankingType}-ranking`).style.display = 'block';
  
  currentRanking = rankingType;
  loadRankingData(rankingType);
}

function loadRankingData(rankingType) {
  const listContainer = document.getElementById(`${rankingType}-list`);
  if (!listContainer) return;
  
  let rankedRestaurants = [];
  
  switch (rankingType) {
    case 'overall':
      rankedRestaurants = [...restaurants]
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 50);
      break;
      
    case 'new':
      // Simulate new restaurants (in real app, would filter by opening date)
      rankedRestaurants = [...restaurants]
        .filter(r => r.badges.includes('popular'))
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 20);
      break;
      
    case 'budget':
      // Filter by price range (assuming RM 5-25 is budget)
      rankedRestaurants = [...restaurants]
        .filter(r => {
          const priceMatch = r.priceRange.match(/RM (\d+)-(\d+)/);
          if (priceMatch) {
            const maxPrice = parseInt(priceMatch[2]);
            return maxPrice <= 25;
          }
          return false;
        })
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 30);
      break;
      
    case 'luxury':
      // Filter by price range (assuming RM 50+ is luxury)
      rankedRestaurants = [...restaurants]
        .filter(r => {
          const priceMatch = r.priceRange.match(/RM (\d+)-(\d+)/);
          if (priceMatch) {
            const minPrice = parseInt(priceMatch[1]);
            return minPrice >= 50;
          }
          return false;
        })
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 20);
      break;
      
    case 'local':
      rankedRestaurants = [...restaurants]
        .filter(r => r.badges.includes('local-favorite'))
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 25);
      break;
      
    case 'halal':
      rankedRestaurants = [...restaurants]
        .filter(r => r.isHalal)
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 30);
      break;
      
    default:
      rankedRestaurants = [...restaurants].slice(0, 20);
  }
  
  listContainer.innerHTML = rankedRestaurants.map((restaurant, index) => 
    createRankingItem(restaurant, index + 1)
  ).join('');
  
  // Initialize favorite buttons
  initializeFavoriteButtons();
}

function createRankingItem(restaurant, rank) {
  const isFavorite = window.EatMY.favorites.includes(restaurant.id);
  const badges = restaurant.badges.map(badge => 
    `<span class="badge badge-${badge}">${badge.replace('-', ' ')}</span>`
  ).join('');
  
  // Determine rank styling
  let rankClass = '';
  let rankIcon = '';
  if (rank === 1) {
    rankClass = 'rank-gold';
    rankIcon = '🥇';
  } else if (rank === 2) {
    rankClass = 'rank-silver';
    rankIcon = '🥈';
  } else if (rank === 3) {
    rankClass = 'rank-bronze';
    rankIcon = '🥉';
  }
  
  return `
    <div class="ranking-item" style="background: var(--white); border-radius: var(--radius-xl); padding: var(--space-6); box-shadow: var(--shadow-sm); margin-bottom: var(--space-4); transition: all var(--transition-fast); cursor: pointer;" onclick="window.location.href='restaurant-detail.html?id=${restaurant.id}'">
      <div style="display: flex; gap: var(--space-6); align-items: center;">
        <!-- Rank -->
        <div class="rank-number ${rankClass}" style="min-width: 60px; text-align: center;">
          ${rank <= 3 ? `<div style="font-size: 24px;">${rankIcon}</div>` : ''}
          <div style="font-size: ${rank <= 3 ? 'var(--text-lg)' : 'var(--text-2xl)'}; font-weight: 700; color: ${rank <= 3 ? 'var(--primary-color)' : 'var(--gray-600)'};">
            #${rank}
          </div>
        </div>
        
        <!-- Restaurant Image -->
        <div style="position: relative;">
          <img 
            src="${restaurant.image}" 
            alt="${restaurant.name}"
            style="width: 120px; height: 120px; object-fit: cover; border-radius: var(--radius-lg);"
            loading="lazy"
          >
          <button 
            class="favorite-btn" 
            data-restaurant-id="${restaurant.id}"
            onclick="event.stopPropagation(); window.EatMY.toggleFavorite(${restaurant.id})"
            style="position: absolute; top: 8px; right: 8px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 32px; height: 32px; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 14px;"
          >
            ${isFavorite ? '❤️' : '🤍'}
          </button>
        </div>
        
        <!-- Restaurant Info -->
        <div style="flex: 1;">
          <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: var(--space-2);">
            <h3 style="font-size: var(--text-xl); font-weight: 600; margin: 0; color: var(--gray-900);">
              ${restaurant.name}
            </h3>
            ${restaurant.isHalal ? '<span class="badge badge-halal">Halal</span>' : ''}
          </div>
          
          <p style="color: var(--gray-600); margin-bottom: var(--space-3);">
            ${getCuisineDisplayName(restaurant.cuisine)} • ${restaurant.area} • ${restaurant.priceRange}
          </p>
          
          <div style="display: flex; align-items: center; gap: var(--space-4); margin-bottom: var(--space-3);">
            <div class="rating">
              <div class="rating-stars">
                ${createStarRating(restaurant.rating)}
              </div>
              <span class="rating-value" style="font-weight: 600;">${restaurant.rating}</span>
              <span class="rating-count" style="color: var(--gray-500);">(${restaurant.reviewCount} reviews)</span>
            </div>
            
            <div style="display: flex; align-items: center; gap: var(--space-2);">
              ${restaurant.isOpen ? 
                '<span class="badge badge-success">Open Now</span>' : 
                '<span class="badge" style="background: var(--error); color: white;">Closed</span>'
              }
            </div>
          </div>
          
          <div style="display: flex; gap: var(--space-2); flex-wrap: wrap;">
            ${badges}
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div style="display: flex; flex-direction: column; gap: var(--space-2); min-width: 120px;">
          <button class="btn btn-primary btn-sm" onclick="event.stopPropagation(); window.location.href='restaurant-detail.html?id=${restaurant.id}'">
            View Details
          </button>
          <button class="btn btn-outline btn-sm" onclick="event.stopPropagation(); window.location.href='review.html?restaurant=${restaurant.id}'">
            Write Review
          </button>
        </div>
      </div>
    </div>
  `;
}

function createStarRating(rating) {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  let stars = '';
  
  for (let i = 0; i < fullStars; i++) {
    stars += '<span class="rating-star filled">★</span>';
  }
  
  if (hasHalfStar) {
    stars += '<span class="rating-star half">☆</span>';
  }
  
  for (let i = 0; i < emptyStars; i++) {
    stars += '<span class="rating-star">☆</span>';
  }
  
  return stars;
}

function getCuisineDisplayName(cuisine) {
  const cuisineMap = {
    'malay': 'Malay',
    'chinese': 'Chinese',
    'indian': 'Indian',
    'western': 'Western',
    'japanese': 'Japanese',
    'korean': 'Korean',
    'thai': 'Thai',
    'fusion': 'Fusion'
  };
  
  return cuisineMap[cuisine] || cuisine;
}

function initializeFavoriteButtons() {
  const favoriteButtons = document.querySelectorAll('.favorite-btn');
  favoriteButtons.forEach(button => {
    const restaurantId = parseInt(button.dataset.restaurantId);
    const isFavorite = window.EatMY.favorites.includes(restaurantId);
    
    button.innerHTML = isFavorite ? '❤️' : '🤍';
  });
}

// Add hover effects to ranking items
document.addEventListener('DOMContentLoaded', function() {
  setTimeout(() => {
    const rankingItems = document.querySelectorAll('.ranking-item');
    rankingItems.forEach(item => {
      item.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = 'var(--shadow-lg)';
      });
      
      item.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = 'var(--shadow-sm)';
      });
    });
  }, 100);
});

// Add custom styles for ranking tabs
document.addEventListener('DOMContentLoaded', function() {
  const style = document.createElement('style');
  style.textContent = `
    .ranking-tab {
      padding: var(--space-3) var(--space-6);
      border: none;
      background: var(--gray-100);
      color: var(--gray-700);
      border-radius: var(--radius-lg);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
      white-space: nowrap;
    }
    
    .ranking-tab:hover {
      background: var(--gray-200);
    }
    
    .ranking-tab.active {
      background: var(--primary-color);
      color: var(--white);
    }
    
    .rankings-tabs {
      display: flex;
      gap: var(--space-2);
      overflow-x: auto;
      padding-bottom: var(--space-2);
    }
    
    .rank-gold {
      color: #FFD700 !important;
    }
    
    .rank-silver {
      color: #C0C0C0 !important;
    }
    
    .rank-bronze {
      color: #CD7F32 !important;
    }
    
    @media (max-width: 768px) {
      .ranking-item > div {
        flex-direction: column !important;
        text-align: center;
      }
      
      .ranking-item .rank-number {
        order: -1;
      }
    }
  `;
  document.head.appendChild(style);
});
