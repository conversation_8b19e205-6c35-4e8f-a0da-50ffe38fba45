// EatMY - Rankings Page Functionality

// 使用全局变量而不是ES6模块导入，避免CORS问题
// restaurants数据将从data.js全局变量中获取

let currentRanking = 'overall';

// Initialize rankings page
document.addEventListener('DOMContentLoaded', function() {
  initializeRankingsPage();
});

function initializeRankingsPage() {
  initializeRankingTabs();
  loadRankingData(currentRanking);
  
  console.log('Rankings page initialized');
}

function initializeRankingTabs() {
  const rankingTabs = document.querySelectorAll('.luxury-tab');

  rankingTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const rankingType = this.dataset.ranking;
      switchRanking(rankingType);
    });
  });
}

function switchRanking(rankingType) {
  // Update active tab
  document.querySelectorAll('.luxury-tab').forEach(tab => {
    tab.classList.remove('active');
  });
  document.querySelector(`[data-ranking="${rankingType}"]`).classList.add('active');

  // Update active section
  document.querySelectorAll('.ranking-section').forEach(section => {
    section.style.display = 'none';
  });
  document.getElementById(`${rankingType}-ranking`).style.display = 'block';

  currentRanking = rankingType;
  loadRankingData(rankingType);
}

function loadRankingData(rankingType) {
  const listContainer = document.getElementById(`${rankingType}-list`);
  if (!listContainer) return;

  // 确保restaurants数据已加载
  if (!window.restaurants) {
    console.error('Restaurant data not loaded yet');
    return;
  }

  let rankedRestaurants = [];

  switch (rankingType) {
    case 'overall':
      rankedRestaurants = [...window.restaurants]
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 50);
      break;

    case 'new':
      // Simulate new restaurants (in real app, would filter by opening date)
      rankedRestaurants = [...window.restaurants]
        .filter(r => r.badges.includes('popular'))
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 20);
      break;

    case 'budget':
      // Filter by price range (assuming RM 5-25 is budget)
      rankedRestaurants = [...window.restaurants]
        .filter(r => {
          const priceMatch = r.priceRange.match(/RM (\d+)-(\d+)/);
          if (priceMatch) {
            const maxPrice = parseInt(priceMatch[2]);
            return maxPrice <= 25;
          }
          return false;
        })
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 30);
      break;

    case 'luxury':
      // Filter by price range (assuming RM 50+ is luxury)
      rankedRestaurants = [...window.restaurants]
        .filter(r => {
          const priceMatch = r.priceRange.match(/RM (\d+)-(\d+)/);
          if (priceMatch) {
            const minPrice = parseInt(priceMatch[1]);
            return minPrice >= 50;
          }
          return false;
        })
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 20);
      break;

    case 'local':
      rankedRestaurants = [...window.restaurants]
        .filter(r => r.badges.includes('local-favorite'))
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 25);
      break;

    case 'halal':
      rankedRestaurants = [...window.restaurants]
        .filter(r => r.isHalal)
        .sort((a, b) => b.rating - a.rating)
        .slice(0, 30);
      break;

    default:
      rankedRestaurants = [...window.restaurants].slice(0, 20);
  }
  
  listContainer.innerHTML = rankedRestaurants.map((restaurant, index) => 
    createRankingItem(restaurant, index + 1)
  ).join('');
  
  // Initialize favorite buttons
  initializeFavoriteButtons();
}

function createRankingItem(restaurant, rank) {
  const isFavorite = window.EatMY.favorites.includes(restaurant.id);
  const badges = restaurant.badges.map(badge =>
    `<span class="luxury-badge badge-${badge}">${badge.replace('-', ' ')}</span>`
  ).join('');

  // Determine luxury rank styling
  let rankClass = '';
  let rankIcon = '';
  let rankBg = '';
  let rankBorder = '';

  if (rank === 1) {
    rankClass = 'rank-gold';
    rankIcon = '👑';
    rankBg = 'linear-gradient(135deg, #FFD700, #FFA500)';
    rankBorder = '3px solid #FFD700';
  } else if (rank === 2) {
    rankClass = 'rank-silver';
    rankIcon = '🥈';
    rankBg = 'linear-gradient(135deg, #C0C0C0, #A8A8A8)';
    rankBorder = '3px solid #C0C0C0';
  } else if (rank === 3) {
    rankClass = 'rank-bronze';
    rankIcon = '🥉';
    rankBg = 'linear-gradient(135deg, #CD7F32, #B8860B)';
    rankBorder = '3px solid #CD7F32';
  } else if (rank <= 10) {
    rankBg = 'linear-gradient(135deg, #667eea, #764ba2)';
    rankBorder = '2px solid #667eea';
  } else {
    rankBg = 'linear-gradient(135deg, #f093fb, #f5576c)';
    rankBorder = '2px solid #f093fb';
  }

  return `
    <div class="luxury-ranking-item" style="background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); border-radius: var(--radius-xl); padding: var(--space-8); box-shadow: 0 8px 32px rgba(0,0,0,0.1); margin-bottom: var(--space-6); transition: all var(--transition-normal); cursor: pointer; border: 1px solid rgba(255,215,0,0.2); position: relative; overflow: hidden;" onclick="window.location.href='restaurant-detail.html?id=${restaurant.id}'">

      <!-- Luxury Background Pattern -->
      ${rank <= 3 ? `<div style="position: absolute; top: 0; right: 0; width: 100px; height: 100px; background: radial-gradient(circle, rgba(255,215,0,0.1) 0%, transparent 70%); border-radius: 0 var(--radius-xl) 0 100%;"></div>` : ''}

      <div style="display: flex; gap: var(--space-8); align-items: center; position: relative; z-index: 2;">
        <!-- Luxury Rank -->
        <div class="luxury-rank ${rankClass}" style="min-width: 80px; text-align: center; position: relative;">
          <div style="width: 80px; height: 80px; background: ${rankBg}; border-radius: 50%; display: flex; align-items: center; justify-content: center; border: ${rankBorder}; box-shadow: 0 4px 16px rgba(0,0,0,0.2); position: relative;">
            ${rank <= 3 ? `
              <div style="font-size: 28px; filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));">${rankIcon}</div>
            ` : `
              <div style="font-size: var(--text-xl); font-weight: 800; color: white; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">#${rank}</div>
            `}

            ${rank <= 3 ? `<div style="position: absolute; top: -5px; right: -5px; width: 20px; height: 20px; background: #FFD700; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 10px; color: #1a1a1a; font-weight: 800;">${rank}</div>` : ''}
          </div>

          ${rank <= 3 ? `<div style="margin-top: var(--space-2); font-size: var(--text-xs); font-weight: 600; color: #FFD700; text-transform: uppercase; letter-spacing: 1px;">${rank === 1 ? 'CHAMPION' : rank === 2 ? 'RUNNER-UP' : 'BRONZE'}</div>` : ''}
        </div>

        <!-- Luxury Restaurant Image -->
        <div style="position: relative;">
          <div style="width: 140px; height: 140px; border-radius: var(--radius-xl); overflow: hidden; box-shadow: 0 8px 24px rgba(0,0,0,0.15); border: 3px solid rgba(255,215,0,0.3);">
            <img
              src="${restaurant.image}"
              alt="${restaurant.name}"
              style="width: 100%; height: 100%; object-fit: cover; transition: transform var(--transition-normal);"
              loading="lazy"
              onmouseover="this.style.transform='scale(1.1)'"
              onmouseout="this.style.transform='scale(1)'"
            >
          </div>

          <button
            class="luxury-favorite-btn"
            data-restaurant-id="${restaurant.id}"
            onclick="event.stopPropagation(); window.EatMY.toggleFavorite(${restaurant.id})"
            style="position: absolute; top: 12px; right: 12px; background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(255,255,255,0.8)); border: 2px solid rgba(255,215,0,0.5); border-radius: 50%; width: 40px; height: 40px; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 18px; box-shadow: 0 4px 12px rgba(0,0,0,0.2); backdrop-filter: blur(10px);"
          >
            ${isFavorite ? '❤️' : '🤍'}
          </button>

          ${rank <= 3 ? `<div style="position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); background: ${rankBg}; color: white; padding: 4px 12px; border-radius: 20px; font-size: var(--text-xs); font-weight: 700; text-transform: uppercase; letter-spacing: 1px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);">${rank === 1 ? 'ELITE' : rank === 2 ? 'PREMIUM' : 'SELECT'}</div>` : ''}
        </div>

        <!-- Luxury Restaurant Info -->
        <div style="flex: 1;">
          <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: var(--space-3);">
            <div>
              <h3 style="font-size: var(--text-2xl); font-weight: 700; margin: 0; color: var(--gray-900); line-height: 1.2;">
                ${restaurant.name}
              </h3>
              <div style="margin-top: var(--space-1); display: flex; align-items: center; gap: var(--space-2);">
                ${restaurant.isHalal ? '<span class="luxury-badge halal">✅ Halal Certified</span>' : ''}
                ${rank <= 10 ? '<span class="luxury-badge elite">⭐ Top 10</span>' : ''}
              </div>
            </div>
          </div>

          <p style="color: var(--gray-600); margin-bottom: var(--space-4); font-size: var(--text-lg);">
            <span style="font-weight: 600; color: var(--primary-color);">${getCuisineDisplayName(restaurant.cuisine)}</span> •
            <span style="color: var(--gray-700);">${restaurant.area}</span> •
            <span style="font-weight: 500; color: var(--gray-800);">${restaurant.priceRange}</span>
          </p>

          <div style="display: flex; align-items: center; gap: var(--space-6); margin-bottom: var(--space-4);">
            <div class="luxury-rating" style="display: flex; align-items: center; gap: var(--space-3); background: linear-gradient(135deg, #f8f9fa, #ffffff); padding: var(--space-3) var(--space-4); border-radius: var(--radius-lg); border: 1px solid rgba(255,215,0,0.3);">
              <div class="rating-stars" style="font-size: 18px;">
                ${createLuxuryStarRating(restaurant.rating)}
              </div>
              <span class="rating-value" style="font-weight: 700; font-size: var(--text-lg); color: var(--primary-color);">${restaurant.rating}</span>
              <span class="rating-count" style="color: var(--gray-500); font-weight: 500;">(${restaurant.reviewCount} reviews)</span>
            </div>

            <div style="display: flex; align-items: center; gap: var(--space-2);">
              ${restaurant.isOpen ?
                '<span class="luxury-status open">🟢 Open Now</span>' :
                '<span class="luxury-status closed">🔴 Closed</span>'
              }
            </div>
          </div>

          <div style="display: flex; gap: var(--space-2); flex-wrap: wrap;">
            ${badges}
          </div>
        </div>

        <!-- Luxury Action Buttons -->
        <div style="display: flex; flex-direction: column; gap: var(--space-3); min-width: 140px;">
          <button class="luxury-btn primary" onclick="event.stopPropagation(); window.location.href='restaurant-detail.html?id=${restaurant.id}'" style="background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)); color: white; border: none; padding: var(--space-3) var(--space-4); border-radius: var(--radius-lg); font-weight: 600; cursor: pointer; transition: all var(--transition-fast); box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
            ✨ View Details
          </button>
          <button class="luxury-btn secondary" onclick="event.stopPropagation(); window.location.href='review.html?restaurant=${restaurant.id}'" style="background: linear-gradient(135deg, #ffffff, #f8f9fa); color: var(--gray-700); border: 2px solid rgba(255,215,0,0.5); padding: var(--space-3) var(--space-4); border-radius: var(--radius-lg); font-weight: 600; cursor: pointer; transition: all var(--transition-fast);">
            📝 Write Review
          </button>

          ${rank <= 3 ? `
            <div style="text-align: center; margin-top: var(--space-2);">
              <div style="font-size: var(--text-xs); color: #FFD700; font-weight: 700; text-transform: uppercase; letter-spacing: 1px;">
                🏆 ${rank === 1 ? 'CHAMPION' : rank === 2 ? 'ELITE' : 'PREMIUM'}
              </div>
            </div>
          ` : ''}
        </div>
      </div>
    </div>
  `;
}

function createLuxuryStarRating(rating) {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  let stars = '';

  for (let i = 0; i < fullStars; i++) {
    stars += '<span style="color: #FFD700; text-shadow: 1px 1px 2px rgba(0,0,0,0.2);">★</span>';
  }

  if (hasHalfStar) {
    stars += '<span style="color: #FFD700; text-shadow: 1px 1px 2px rgba(0,0,0,0.2);">☆</span>';
  }

  for (let i = 0; i < emptyStars; i++) {
    stars += '<span style="color: #ddd;">☆</span>';
  }

  return stars;
}

function createStarRating(rating) {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  let stars = '';
  
  for (let i = 0; i < fullStars; i++) {
    stars += '<span class="rating-star filled">★</span>';
  }
  
  if (hasHalfStar) {
    stars += '<span class="rating-star half">☆</span>';
  }
  
  for (let i = 0; i < emptyStars; i++) {
    stars += '<span class="rating-star">☆</span>';
  }
  
  return stars;
}

function getCuisineDisplayName(cuisine) {
  const cuisineMap = {
    'malay': 'Malay',
    'chinese': 'Chinese',
    'indian': 'Indian',
    'western': 'Western',
    'japanese': 'Japanese',
    'korean': 'Korean',
    'thai': 'Thai',
    'fusion': 'Fusion'
  };
  
  return cuisineMap[cuisine] || cuisine;
}

function initializeFavoriteButtons() {
  const favoriteButtons = document.querySelectorAll('.favorite-btn');
  favoriteButtons.forEach(button => {
    const restaurantId = parseInt(button.dataset.restaurantId);
    const isFavorite = window.EatMY.favorites.includes(restaurantId);
    
    button.innerHTML = isFavorite ? '❤️' : '🤍';
  });
}

// Add hover effects to ranking items
document.addEventListener('DOMContentLoaded', function() {
  setTimeout(() => {
    const rankingItems = document.querySelectorAll('.ranking-item');
    rankingItems.forEach(item => {
      item.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = 'var(--shadow-lg)';
      });
      
      item.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = 'var(--shadow-sm)';
      });
    });
  }, 100);
});

// Add luxury styles for ranking tabs and items
document.addEventListener('DOMContentLoaded', function() {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes rotate {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    @keyframes shimmer {
      0% { background-position: -200px 0; }
      100% { background-position: calc(200px + 100%) 0; }
    }

    .luxury-tab {
      padding: var(--space-4) var(--space-6);
      border: none;
      background: linear-gradient(135deg, #ffffff, #f8f9fa);
      color: var(--gray-700);
      border-radius: var(--radius-xl);
      font-weight: 600;
      cursor: pointer;
      transition: all var(--transition-normal);
      white-space: nowrap;
      min-width: 160px;
      text-align: center;
      border: 2px solid transparent;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      position: relative;
      overflow: hidden;
    }

    .luxury-tab::before {
      content: '';
      position: absolute;
      top: 0;
      left: -200px;
      width: 200px;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
      transition: left 0.5s;
    }

    .luxury-tab:hover::before {
      left: 100%;
    }

    .luxury-tab:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0,0,0,0.15);
      border-color: rgba(255,215,0,0.5);
    }

    .luxury-tab.active {
      background: linear-gradient(135deg, #FFD700, #FFA500);
      color: #1a1a1a;
      border-color: #FFD700;
      box-shadow: 0 8px 32px rgba(255,215,0,0.4);
      transform: translateY(-2px);
    }

    .luxury-tab .tab-icon {
      display: block;
      font-size: 24px;
      margin-bottom: var(--space-1);
    }

    .luxury-tab .tab-text {
      display: block;
      font-size: var(--text-sm);
      font-weight: 800;
      letter-spacing: 0.5px;
      margin-bottom: 2px;
    }

    .luxury-tab .tab-subtitle {
      display: block;
      font-size: var(--text-xs);
      opacity: 0.8;
      font-weight: 500;
    }

    .luxury-tabs {
      display: flex;
      gap: var(--space-3);
      overflow-x: auto;
      padding: var(--space-4) 0;
    }

    .luxury-tabs::-webkit-scrollbar {
      height: 6px;
    }

    .luxury-tabs::-webkit-scrollbar-track {
      background: rgba(255,215,0,0.1);
      border-radius: 3px;
    }

    .luxury-tabs::-webkit-scrollbar-thumb {
      background: linear-gradient(90deg, #FFD700, #FFA500);
      border-radius: 3px;
    }

    .luxury-ranking-item:hover {
      transform: translateY(-4px);
      box-shadow: 0 16px 48px rgba(0,0,0,0.15);
      border-color: rgba(255,215,0,0.4);
    }

    .luxury-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: var(--text-xs);
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .luxury-badge.halal {
      background: linear-gradient(135deg, #27ae60, #2ecc71);
      color: white;
    }

    .luxury-badge.elite {
      background: linear-gradient(135deg, #FFD700, #FFA500);
      color: #1a1a1a;
    }

    .luxury-status {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: var(--text-sm);
      font-weight: 600;
    }

    .luxury-status.open {
      background: linear-gradient(135deg, #27ae60, #2ecc71);
      color: white;
    }

    .luxury-status.closed {
      background: linear-gradient(135deg, #e74c3c, #c0392b);
      color: white;
    }

    .luxury-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(0,0,0,0.2) !important;
    }

    .luxury-btn.primary:hover {
      background: linear-gradient(135deg, var(--primary-dark), var(--primary-color)) !important;
    }

    .luxury-btn.secondary:hover {
      background: linear-gradient(135deg, #f8f9fa, #ffffff) !important;
      border-color: #FFD700 !important;
    }

    @media (max-width: 768px) {
      .luxury-ranking-item > div {
        flex-direction: column !important;
        text-align: center;
        gap: var(--space-4) !important;
      }

      .luxury-rank {
        order: -1;
      }

      .luxury-tab {
        min-width: 120px;
        padding: var(--space-3) var(--space-4);
      }

      .luxury-tab .tab-icon {
        font-size: 20px;
      }

      .luxury-tab .tab-text {
        font-size: var(--text-xs);
      }
    }
  `;
  document.head.appendChild(style);
});
