// EatMY - Restaurant Detail Page Functionality

import { getRestaurantById, getReviewsByRestaurant, restaurants, reviews } from './data.js';

let currentRestaurant = null;
let currentReviews = [];
let displayedReviews = [];
let reviewsPerPage = 5;
let currentReviewPage = 1;

// Initialize restaurant detail page
document.addEventListener('DOMContentLoaded', function() {
  initializeRestaurantDetail();
});

function initializeRestaurantDetail() {
  // Get restaurant ID from URL
  const urlParams = new URLSearchParams(window.location.search);
  const restaurantId = urlParams.get('id');
  
  if (!restaurantId) {
    window.location.href = 'index.html';
    return;
  }
  
  // Load restaurant data
  currentRestaurant = getRestaurantById(restaurantId);
  if (!currentRestaurant) {
    window.location.href = 'search.html';
    return;
  }
  
  // Load reviews
  currentReviews = getReviewsByRestaurant(restaurantId);
  
  // Render page content
  renderRestaurantDetail();
  renderReviews();
  renderSimilarRestaurants();
  
  // Initialize interactions
  initializeImageGallery();
  initializeReviewFilters();
  initializeFavoriteButton();
  
  console.log('Restaurant detail page initialized');
}

function renderRestaurantDetail() {
  // Update page title and breadcrumb
  document.title = `${currentRestaurant.name} - EatMY`;
  document.getElementById('restaurant-breadcrumb').textContent = currentRestaurant.name;
  
  // Render hero section
  renderHeroSection();
  
  // Render description and specialties
  renderDescription();
  
  // Render sidebar info
  renderQuickInfo();
  renderRatingBreakdown();
  renderOpeningHours();
  renderLocationInfo();
  
  // Render image gallery
  renderImageGallery();
}

function renderHeroSection() {
  const heroContainer = document.getElementById('restaurant-hero');
  const isFavorite = window.EatMY.favorites.includes(currentRestaurant.id);
  
  const badges = currentRestaurant.badges.map(badge => 
    `<span class="badge badge-${badge}">${badge.replace('-', ' ')}</span>`
  ).join('');
  
  heroContainer.innerHTML = `
    <img src="${currentRestaurant.images[0] || currentRestaurant.image}" alt="${currentRestaurant.name}" class="restaurant-hero-image">
    <div class="restaurant-hero-overlay">
      <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: var(--space-4);">
        <div>
          <h1 class="restaurant-hero-title">${currentRestaurant.name}</h1>
          <p class="restaurant-hero-subtitle">${getCuisineDisplayName(currentRestaurant.cuisine)} • ${currentRestaurant.area}</p>
        </div>
        <button 
          class="favorite-btn-large" 
          data-restaurant-id="${currentRestaurant.id}"
          onclick="window.EatMY.toggleFavorite(${currentRestaurant.id})"
          style="background: rgba(255,255,255,0.2); border: none; border-radius: 50%; width: 56px; height: 56px; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 24px; backdrop-filter: blur(10px);"
        >
          ${isFavorite ? '❤️' : '🤍'}
        </button>
      </div>
      
      <div style="display: flex; gap: var(--space-4); align-items: center; margin-bottom: var(--space-4);">
        <div class="rating" style="color: white;">
          <div class="rating-stars">
            ${createStarRating(currentRestaurant.rating)}
          </div>
          <span class="rating-value" style="color: white;">${currentRestaurant.rating}</span>
          <span class="rating-count" style="color: rgba(255,255,255,0.8);">(${currentRestaurant.reviewCount} reviews)</span>
        </div>
        
        <div style="color: rgba(255,255,255,0.9);">
          ${currentRestaurant.priceRange}
        </div>
        
        ${currentRestaurant.isOpen ? 
          '<span class="badge badge-success">Open Now</span>' : 
          '<span class="badge" style="background: var(--error);">Closed</span>'
        }
        
        ${currentRestaurant.isHalal ? '<span class="badge badge-halal">Halal</span>' : ''}
      </div>
      
      <div style="display: flex; gap: var(--space-2); margin-bottom: var(--space-4);">
        ${badges}
      </div>
      
      <div class="restaurant-hero-actions">
        <a href="review.html?restaurant=${currentRestaurant.id}" class="btn btn-primary">Write Review</a>
        <button class="btn btn-secondary" onclick="EatMY.openModal('share-modal')">Share</button>
        <button class="btn btn-outline" onclick="openDirections()">Get Directions</button>
      </div>
    </div>
  `;
}

function renderDescription() {
  const descriptionContainer = document.getElementById('restaurant-description');
  descriptionContainer.innerHTML = `
    <h3 style="margin-bottom: var(--space-4);">About ${currentRestaurant.name}</h3>
    <p style="color: var(--gray-700); line-height: 1.6; margin-bottom: var(--space-6);">
      ${currentRestaurant.description}
    </p>
  `;
  
  const specialtiesContainer = document.getElementById('restaurant-specialties');
  specialtiesContainer.innerHTML = `
    <h3 style="margin-bottom: var(--space-4);">Specialties</h3>
    <div style="display: flex; flex-wrap: wrap; gap: var(--space-2);">
      ${currentRestaurant.specialties.map(specialty => 
        `<span class="badge" style="background: var(--gray-200); color: var(--gray-800);">${specialty}</span>`
      ).join('')}
    </div>
  `;
}

function renderQuickInfo() {
  const quickInfoContainer = document.getElementById('quick-info');
  quickInfoContainer.innerHTML = `
    <h3 class="info-card-title">Quick Info</h3>
    <div style="display: flex; flex-direction: column; gap: var(--space-3);">
      <div style="display: flex; justify-content: space-between;">
        <span style="color: var(--gray-600);">Cuisine:</span>
        <span style="font-weight: 500;">${getCuisineDisplayName(currentRestaurant.cuisine)}</span>
      </div>
      <div style="display: flex; justify-content: space-between;">
        <span style="color: var(--gray-600);">Price Range:</span>
        <span style="font-weight: 500;">${currentRestaurant.priceRange}</span>
      </div>
      <div style="display: flex; justify-content: space-between;">
        <span style="color: var(--gray-600);">Phone:</span>
        <a href="tel:${currentRestaurant.phone}" style="font-weight: 500; color: var(--primary-color);">${currentRestaurant.phone}</a>
      </div>
      <div style="display: flex; justify-content: space-between;">
        <span style="color: var(--gray-600);">Status:</span>
        <span style="font-weight: 500; color: ${currentRestaurant.isOpen ? 'var(--success)' : 'var(--error)'};">
          ${currentRestaurant.isOpen ? 'Open Now' : 'Closed'}
        </span>
      </div>
      ${currentRestaurant.isHalal ? `
        <div style="display: flex; justify-content: space-between;">
          <span style="color: var(--gray-600);">Halal:</span>
          <span style="font-weight: 500; color: var(--success);">✅ Certified</span>
        </div>
      ` : ''}
    </div>
  `;
}

function renderRatingBreakdown() {
  const ratingContainer = document.getElementById('rating-breakdown');
  const ratings = currentRestaurant.ratings;
  
  ratingContainer.innerHTML = `
    <h3 class="info-card-title">Rating Breakdown</h3>
    <div class="rating-breakdown">
      <div class="rating-item">
        <span class="rating-label">Taste</span>
        <div class="rating-bar">
          <div class="rating-fill" style="width: ${(ratings.taste / 5) * 100}%"></div>
        </div>
        <span class="rating-score">${ratings.taste}</span>
      </div>
      <div class="rating-item">
        <span class="rating-label">Environment</span>
        <div class="rating-bar">
          <div class="rating-fill" style="width: ${(ratings.environment / 5) * 100}%"></div>
        </div>
        <span class="rating-score">${ratings.environment}</span>
      </div>
      <div class="rating-item">
        <span class="rating-label">Service</span>
        <div class="rating-bar">
          <div class="rating-fill" style="width: ${(ratings.service / 5) * 100}%"></div>
        </div>
        <span class="rating-score">${ratings.service}</span>
      </div>
    </div>
  `;
}

function renderOpeningHours() {
  const hoursContainer = document.getElementById('opening-hours');
  const hours = currentRestaurant.openingHours;
  const today = new Date().toLocaleLowerCase().slice(0, 3) + 'day'; // e.g., 'monday'
  
  const daysOrder = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  const dayNames = {
    monday: 'Monday',
    tuesday: 'Tuesday', 
    wednesday: 'Wednesday',
    thursday: 'Thursday',
    friday: 'Friday',
    saturday: 'Saturday',
    sunday: 'Sunday'
  };
  
  hoursContainer.innerHTML = `
    <h3 class="info-card-title">Opening Hours</h3>
    <ul class="hours-list">
      ${daysOrder.map(day => `
        <li class="hours-item">
          <span class="hours-day ${day === today ? 'current' : ''}">${dayNames[day]}</span>
          <span class="hours-time ${hours[day] === 'Closed' ? 'closed' : ''} ${day === today ? 'current' : ''}">
            ${hours[day]}
          </span>
        </li>
      `).join('')}
    </ul>
  `;
}

function renderLocationInfo() {
  const locationContainer = document.getElementById('location-info');
  locationContainer.innerHTML = `
    <h3 class="info-card-title">Location</h3>
    <div style="margin-bottom: var(--space-4);">
      <p style="color: var(--gray-700); line-height: 1.5;">
        ${currentRestaurant.address}
      </p>
    </div>
    <div style="display: flex; flex-direction: column; gap: var(--space-3);">
      <button class="btn btn-outline" onclick="openDirections()" style="width: 100%;">
        🗺️ Get Directions
      </button>
      <button class="btn btn-secondary" onclick="openMap()" style="width: 100%;">
        📍 View on Map
      </button>
    </div>
  `;
}

function renderImageGallery() {
  const galleryContainer = document.querySelector('.gallery-grid');
  if (!currentRestaurant.images || currentRestaurant.images.length === 0) {
    document.getElementById('restaurant-gallery').style.display = 'none';
    return;
  }
  
  galleryContainer.innerHTML = currentRestaurant.images.map((image, index) => `
    <img 
      src="${image}" 
      alt="${currentRestaurant.name} photo ${index + 1}"
      class="gallery-image"
      style="width: 100%; height: 200px; object-fit: cover; border-radius: var(--radius-lg); cursor: pointer; transition: transform var(--transition-fast);"
      onclick="openImageModal('${image}')"
      onmouseover="this.style.transform='scale(1.05)'"
      onmouseout="this.style.transform='scale(1)'"
    >
  `).join('');
}

function renderReviews() {
  displayedReviews = currentReviews.slice(0, reviewsPerPage);
  
  // Render review summary
  renderReviewSummary();
  
  // Render reviews list
  renderReviewsList();
}

function renderReviewSummary() {
  const summaryContainer = document.getElementById('review-summary');
  const avgRating = currentRestaurant.rating;
  const totalReviews = currentReviews.length;
  
  // Calculate rating distribution
  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => {
    const count = currentReviews.filter(review => Math.floor(review.rating) === rating).length;
    const percentage = totalReviews > 0 ? (count / totalReviews) * 100 : 0;
    return { rating, count, percentage };
  });
  
  summaryContainer.innerHTML = `
    <div style="display: grid; grid-template-columns: auto 1fr; gap: var(--space-8); align-items: center;">
      <div style="text-align: center;">
        <div style="font-size: 3rem; font-weight: 700; color: var(--primary-color); margin-bottom: var(--space-2);">
          ${avgRating}
        </div>
        <div class="rating-stars" style="margin-bottom: var(--space-2);">
          ${createStarRating(avgRating)}
        </div>
        <div style="color: var(--gray-600);">
          ${totalReviews} reviews
        </div>
      </div>
      
      <div>
        ${ratingDistribution.map(item => `
          <div style="display: flex; align-items: center; gap: var(--space-3); margin-bottom: var(--space-2);">
            <span style="min-width: 60px; font-size: var(--text-sm);">${item.rating} stars</span>
            <div style="flex: 1; height: 8px; background: var(--gray-200); border-radius: 4px; overflow: hidden;">
              <div style="height: 100%; background: var(--accent-color); width: ${item.percentage}%; transition: width var(--transition-normal);"></div>
            </div>
            <span style="min-width: 40px; font-size: var(--text-sm); color: var(--gray-600);">${item.count}</span>
          </div>
        `).join('')}
      </div>
    </div>
  `;
}

function renderReviewsList() {
  const reviewsContainer = document.getElementById('reviews-list');
  
  if (displayedReviews.length === 0) {
    reviewsContainer.innerHTML = `
      <div style="text-align: center; padding: var(--space-16); color: var(--gray-500);">
        <div style="font-size: 48px; margin-bottom: var(--space-4);">💬</div>
        <h3>No reviews yet</h3>
        <p>Be the first to review this restaurant!</p>
        <a href="review.html?restaurant=${currentRestaurant.id}" class="btn btn-primary" style="margin-top: var(--space-4);">Write First Review</a>
      </div>
    `;
    return;
  }
  
  reviewsContainer.innerHTML = displayedReviews.map(review => createReviewHTML(review)).join('');
  
  // Update load more button
  const loadMoreBtn = document.getElementById('load-more-reviews');
  if (displayedReviews.length >= currentReviews.length) {
    loadMoreBtn.style.display = 'none';
  } else {
    loadMoreBtn.style.display = 'block';
    loadMoreBtn.onclick = loadMoreReviews;
  }
}

function createReviewHTML(review) {
  return `
    <div class="review-item">
      <div class="review-header">
        <img src="${review.userAvatar}" alt="${review.userName}" class="review-avatar">
        <div class="review-user-info">
          <div class="review-user-name">${review.userName}</div>
          <div class="review-date">${formatDate(review.date)}</div>
        </div>
        <div class="review-rating">
          <div class="rating-stars">
            ${createStarRating(review.rating)}
          </div>
          <span class="rating-value">${review.rating}</span>
        </div>
      </div>
      
      <div class="review-content">
        ${review.title ? `<h4 class="review-title">${review.title}</h4>` : ''}
        <p class="review-text">${review.content}</p>
        
        ${review.images && review.images.length > 0 ? `
          <div class="review-images">
            ${review.images.map(image => `
              <img src="${image}" alt="Review photo" class="review-image" onclick="openImageModal('${image}')">
            `).join('')}
          </div>
        ` : ''}
      </div>
      
      <div class="review-actions">
        <button class="review-action" onclick="toggleReviewHelpful(${review.id})">
          👍 Helpful (${review.helpful})
        </button>
        <button class="review-action" onclick="toggleReviewReply(${review.id})">
          💬 Reply
        </button>
      </div>
      
      ${review.replies && review.replies.length > 0 ? `
        <div class="review-replies" style="margin-top: var(--space-4); padding-left: var(--space-8); border-left: 2px solid var(--gray-200);">
          ${review.replies.map(reply => `
            <div class="review-reply" style="padding: var(--space-3); background: var(--gray-50); border-radius: var(--radius-lg); margin-bottom: var(--space-2);">
              <div style="font-weight: 500; margin-bottom: var(--space-1);">${reply.userName}</div>
              <div style="font-size: var(--text-sm); color: var(--gray-600); margin-bottom: var(--space-2);">${formatDate(reply.date)}</div>
              <div>${reply.content}</div>
            </div>
          `).join('')}
        </div>
      ` : ''}
    </div>
  `;
}

// Utility functions
function createStarRating(rating) {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 >= 0.5;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
  
  let stars = '';
  
  for (let i = 0; i < fullStars; i++) {
    stars += '<span class="rating-star filled">★</span>';
  }
  
  if (hasHalfStar) {
    stars += '<span class="rating-star half">☆</span>';
  }
  
  for (let i = 0; i < emptyStars; i++) {
    stars += '<span class="rating-star">☆</span>';
  }
  
  return stars;
}

function getCuisineDisplayName(cuisine) {
  const cuisineMap = {
    'malay': 'Malay',
    'chinese': 'Chinese',
    'indian': 'Indian',
    'western': 'Western',
    'japanese': 'Japanese',
    'korean': 'Korean',
    'thai': 'Thai',
    'fusion': 'Fusion'
  };
  
  return cuisineMap[cuisine] || cuisine;
}

function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
}

function renderSimilarRestaurants() {
  const similarContainer = document.getElementById('similar-restaurants');
  
  // Get similar restaurants (same cuisine, different restaurant)
  const similar = restaurants
    .filter(r => r.cuisine === currentRestaurant.cuisine && r.id !== currentRestaurant.id)
    .slice(0, 4);
  
  if (similar.length === 0) {
    document.querySelector('.similar-restaurants').style.display = 'none';
    return;
  }
  
  similarContainer.innerHTML = similar.map(restaurant => `
    <div class="restaurant-card" onclick="window.location.href='restaurant-detail.html?id=${restaurant.id}'" style="cursor: pointer;">
      <img src="${restaurant.image}" alt="${restaurant.name}" class="restaurant-card-image" style="height: 150px;">
      <div class="restaurant-card-content" style="padding: var(--space-4);">
        <h4 style="font-size: var(--text-lg); margin-bottom: var(--space-2);">${restaurant.name}</h4>
        <p style="color: var(--gray-600); font-size: var(--text-sm); margin-bottom: var(--space-3);">${getCuisineDisplayName(restaurant.cuisine)} • ${restaurant.area}</p>
        <div class="rating">
          <div class="rating-stars">${createStarRating(restaurant.rating)}</div>
          <span class="rating-value">${restaurant.rating}</span>
        </div>
      </div>
    </div>
  `).join('');
}

// Event handlers
function initializeImageGallery() {
  // Image gallery is already initialized in renderImageGallery
}

function initializeReviewFilters() {
  const ratingFilter = document.getElementById('review-rating-filter');
  const sortFilter = document.getElementById('review-sort');
  
  if (ratingFilter) {
    ratingFilter.addEventListener('change', filterReviews);
  }
  
  if (sortFilter) {
    sortFilter.addEventListener('change', filterReviews);
  }
}

function initializeFavoriteButton() {
  // Favorite button is already initialized in renderHeroSection
}

function filterReviews() {
  // This would implement review filtering logic
  console.log('Filtering reviews...');
}

function loadMoreReviews() {
  currentReviewPage++;
  const startIndex = (currentReviewPage - 1) * reviewsPerPage;
  const endIndex = startIndex + reviewsPerPage;
  const newReviews = currentReviews.slice(startIndex, endIndex);
  
  displayedReviews = [...displayedReviews, ...newReviews];
  renderReviewsList();
}

function openImageModal(imageSrc) {
  const modal = document.getElementById('image-modal');
  const modalImage = document.getElementById('modal-image');
  
  modalImage.src = imageSrc;
  window.EatMY.openModal('image-modal');
}

function openDirections() {
  const coords = currentRestaurant.coordinates;
  const url = `https://www.google.com/maps/dir/?api=1&destination=${coords.lat},${coords.lng}`;
  window.open(url, '_blank');
}

function openMap() {
  window.location.href = `map.html?restaurant=${currentRestaurant.id}`;
}

function toggleReviewHelpful(reviewId) {
  console.log('Toggle helpful for review:', reviewId);
  // This would implement helpful toggle logic
}

function toggleReviewReply(reviewId) {
  console.log('Toggle reply for review:', reviewId);
  // This would implement reply toggle logic
}

// Make functions available globally
window.openImageModal = openImageModal;
window.openDirections = openDirections;
window.openMap = openMap;
window.toggleReviewHelpful = toggleReviewHelpful;
window.toggleReviewReply = toggleReviewReply;
