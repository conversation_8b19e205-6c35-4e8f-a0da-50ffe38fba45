// EatMY - Mock Data

// Restaurant Categories - Comprehensive Classification System
export const categories = {
  // 菜系分类 (Cuisine Types)
  cuisine: [
    { id: 'malay', name: 'Malay', icon: '🍛', color: '#e74c3c', description: 'Traditional Malaysian cuisine' },
    { id: 'chinese', name: 'Chinese', icon: '🥢', color: '#f39c12', description: 'Authentic Chinese dishes' },
    { id: 'indian', name: 'Indian', icon: '🍛', color: '#e67e22', description: 'Spicy Indian flavors' },
    { id: 'western', name: 'Western', icon: '🍔', color: '#3498db', description: 'International Western food' },
    { id: 'japanese', name: 'Japanese', icon: '🍣', color: '#9b59b6', description: 'Fresh Japanese cuisine' },
    { id: 'korean', name: 'Korean', icon: '🍜', color: '#e91e63', description: 'Korean BBQ and more' },
    { id: 'thai', name: 'Thai', icon: '🌶️', color: '#4caf50', description: 'Authentic Thai flavors' },
    { id: 'fusion', name: 'Fusion', icon: '🍽️', color: '#ff9800', description: 'Creative fusion cuisine' },
    { id: 'italian', name: 'Italian', icon: '🍝', color: '#27ae60', description: 'Pasta, pizza and more' },
    { id: 'mexican', name: 'Mexican', icon: '🌮', color: '#e74c3c', description: 'Spicy Mexican food' },
    { id: 'mediterranean', name: 'Mediterranean', icon: '🫒', color: '#3498db', description: 'Healthy Mediterranean' },
    { id: 'vietnamese', name: 'Vietnamese', icon: '🍜', color: '#2ecc71', description: 'Fresh Vietnamese dishes' }
  ],

  // 区域分类 (Regional Areas)
  regions: [
    { id: 'kl-city', name: 'KL City Center', icon: '🏙️', color: '#e74c3c', description: 'Heart of Kuala Lumpur' },
    { id: 'bangsar', name: 'Bangsar', icon: '🌃', color: '#9b59b6', description: 'Trendy dining district' },
    { id: 'mont-kiara', name: 'Mont Kiara', icon: '🏢', color: '#3498db', description: 'Upscale expat area' },
    { id: 'pj', name: 'Petaling Jaya', icon: '🏘️', color: '#f39c12', description: 'Suburban food paradise' },
    { id: 'penang', name: 'Penang', icon: '🏝️', color: '#e67e22', description: 'Food capital of Malaysia' },
    { id: 'johor', name: 'Johor Bahru', icon: '🌊', color: '#2ecc71', description: 'Southern gateway' },
    { id: 'ipoh', name: 'Ipoh', icon: '⛰️', color: '#95a5a6', description: 'Heritage food town' },
    { id: 'melaka', name: 'Melaka', icon: '🏛️', color: '#e91e63', description: 'Historical flavors' },
    { id: 'kota-kinabalu', name: 'Kota Kinabalu', icon: '🏔️', color: '#16a085', description: 'Sabah seafood hub' },
    { id: 'kuching', name: 'Kuching', icon: '🐱', color: '#f39c12', description: 'Sarawak delicacies' }
  ],

  // 使用场景 (Dining Occasions)
  occasions: [
    { id: 'date-night', name: 'Date Night', icon: '💕', color: '#e91e63', description: 'Romantic dining' },
    { id: 'family-dinner', name: 'Family Dinner', icon: '👨‍👩‍👧‍👦', color: '#3498db', description: 'Family-friendly restaurants' },
    { id: 'business-lunch', name: 'Business Lunch', icon: '💼', color: '#34495e', description: 'Professional dining' },
    { id: 'celebration', name: 'Celebration', icon: '🎉', color: '#f39c12', description: 'Special occasions' },
    { id: 'casual-hangout', name: 'Casual Hangout', icon: '😎', color: '#2ecc71', description: 'Relaxed dining' },
    { id: 'quick-bite', name: 'Quick Bite', icon: '⚡', color: '#e74c3c', description: 'Fast and convenient' },
    { id: 'late-night', name: 'Late Night', icon: '🌙', color: '#9b59b6', description: 'After hours dining' },
    { id: 'brunch', name: 'Brunch', icon: '🥐', color: '#f39c12', description: 'Weekend brunch spots' }
  ],

  // 特定条件 (Special Conditions)
  conditions: [
    { id: 'halal', name: 'Halal Certified', icon: '✅', color: '#27ae60', description: 'Halal certified restaurants' },
    { id: 'vegetarian', name: 'Vegetarian', icon: '🥬', color: '#2ecc71', description: 'Vegetarian options' },
    { id: 'vegan', name: 'Vegan', icon: '🌱', color: '#27ae60', description: '100% plant-based' },
    { id: 'gluten-free', name: 'Gluten Free', icon: '🌾', color: '#f39c12', description: 'Gluten-free options' },
    { id: 'pet-friendly', name: 'Pet Friendly', icon: '🐕', color: '#e67e22', description: 'Pets welcome' },
    { id: 'wifi', name: 'Free WiFi', icon: '📶', color: '#3498db', description: 'High-speed internet' },
    { id: 'parking', name: 'Parking Available', icon: '🅿️', color: '#95a5a6', description: 'Convenient parking' },
    { id: 'delivery', name: 'Delivery', icon: '🚚', color: '#e74c3c', description: 'Food delivery available' },
    { id: 'outdoor-seating', name: 'Outdoor Seating', icon: '🌿', color: '#2ecc71', description: 'Al fresco dining' },
    { id: 'live-music', name: 'Live Music', icon: '🎵', color: '#9b59b6', description: 'Entertainment dining' },
    { id: 'private-room', name: 'Private Dining', icon: '🏠', color: '#34495e', description: 'Private rooms available' },
    { id: 'buffet', name: 'Buffet', icon: '🍽️', color: '#f39c12', description: 'All-you-can-eat' }
  ]
};

// Malaysian States/Areas
export const areas = [
  'Kuala Lumpur', 'Selangor', 'Penang', 'Johor', 'Perak', 'Kedah', 
  'Kelantan', 'Terengganu', 'Pahang', 'Negeri Sembilan', 'Melaka', 
  'Perlis', 'Sabah', 'Sarawak', 'Labuan', 'Putrajaya'
];

// Mock Restaurants Data - Expanded with Real Unsplash Photos
export const restaurants = [
  {
    id: 1,
    name: "Nasi Lemak Wanjo",
    cuisine: "malay",
    region: "kl-city",
    area: "Kuala Lumpur",
    address: "Jalan Tun Tan Cheng Lock, Kuala Lumpur",
    phone: "+603-2078-3813",
    image: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=800&h=600&fit=crop"
    ],
    rating: 4.5,
    reviewCount: 1247,
    priceRange: "RM 5-15",
    isHalal: true,
    isOpen: true,
    openingHours: {
      monday: "6:00 AM - 2:00 PM",
      tuesday: "6:00 AM - 2:00 PM",
      wednesday: "6:00 AM - 2:00 PM",
      thursday: "6:00 AM - 2:00 PM",
      friday: "6:00 AM - 2:00 PM",
      saturday: "6:00 AM - 2:00 PM",
      sunday: "Closed"
    },
    badges: ["popular", "halal", "local-favorite"],
    occasions: ["quick-bite", "casual-hangout"],
    conditions: ["halal", "parking", "delivery"],
    description: "Famous for authentic nasi lemak with perfectly cooked coconut rice and spicy sambal.",
    specialties: ["Nasi Lemak", "Rendang", "Sambal Sotong"],
    coordinates: { lat: 3.1390, lng: 101.6869 },
    ratings: {
      taste: 4.6,
      environment: 4.2,
      service: 4.4
    }
  },
  {
    id: 2,
    name: "Dim Sum Palace",
    cuisine: "chinese",
    region: "kl-city",
    area: "Kuala Lumpur",
    address: "Jalan Petaling, Chinatown, KL",
    phone: "+603-2072-1234",
    image: "https://images.unsplash.com/photo-1563379091339-03246963d96a?w=400&h=300&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1563379091339-03246963d96a?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1496116218417-1a781b1c416c?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1582878826629-29b7ad1cdc43?w=800&h=600&fit=crop"
    ],
    rating: 4.3,
    reviewCount: 892,
    priceRange: "RM 15-40",
    isHalal: false,
    isOpen: true,
    openingHours: {
      monday: "7:00 AM - 3:00 PM",
      tuesday: "7:00 AM - 3:00 PM",
      wednesday: "7:00 AM - 3:00 PM",
      thursday: "7:00 AM - 3:00 PM",
      friday: "7:00 AM - 3:00 PM",
      saturday: "7:00 AM - 4:00 PM",
      sunday: "7:00 AM - 4:00 PM"
    },
    badges: ["high-rating"],
    occasions: ["family-dinner", "brunch"],
    conditions: ["wifi", "parking"],
    description: "Traditional dim sum restaurant serving handmade dumplings and tea.",
    specialties: ["Har Gow", "Siu Mai", "Char Siu Bao"],
    coordinates: { lat: 3.1478, lng: 101.6953 },
    ratings: {
      taste: 4.4,
      environment: 4.1,
      service: 4.4
    }
  },
  {
    id: 3,
    name: "Banana Leaf Restaurant",
    cuisine: "indian",
    area: "Kuala Lumpur",
    address: "Brickfields, Little India, KL",
    phone: "+603-2274-5678",
    image: "https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=400",
    images: [
      "https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=800",
      "https://images.unsplash.com/photo-1574653853027-5d3ac9b9e7c7?w=800"
    ],
    rating: 4.4,
    reviewCount: 654,
    priceRange: "RM 12-25",
    isHalal: true,
    isOpen: false,
    openingHours: {
      monday: "11:00 AM - 10:00 PM",
      tuesday: "11:00 AM - 10:00 PM",
      wednesday: "11:00 AM - 10:00 PM",
      thursday: "11:00 AM - 10:00 PM",
      friday: "11:00 AM - 10:00 PM",
      saturday: "11:00 AM - 11:00 PM",
      sunday: "11:00 AM - 11:00 PM"
    },
    badges: ["halal", "authentic"],
    description: "Authentic South Indian cuisine served on traditional banana leaves.",
    specialties: ["Fish Curry", "Mutton Varuval", "Thosai"],
    coordinates: { lat: 3.1337, lng: 101.6869 },
    ratings: {
      taste: 4.5,
      environment: 4.2,
      service: 4.5
    }
  },
  {
    id: 4,
    name: "The Burger Lab",
    cuisine: "western",
    area: "Selangor",
    address: "Sunway Pyramid, Petaling Jaya",
    phone: "+603-5621-9876",
    image: "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400",
    images: [
      "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=800",
      "https://images.unsplash.com/photo-1550547660-d9450f859349?w=800"
    ],
    rating: 4.2,
    reviewCount: 1156,
    priceRange: "RM 20-45",
    isHalal: false,
    isOpen: true,
    openingHours: {
      monday: "11:00 AM - 10:00 PM",
      tuesday: "11:00 AM - 10:00 PM",
      wednesday: "11:00 AM - 10:00 PM",
      thursday: "11:00 AM - 10:00 PM",
      friday: "11:00 AM - 11:00 PM",
      saturday: "11:00 AM - 11:00 PM",
      sunday: "11:00 AM - 10:00 PM"
    },
    badges: ["popular", "instagram-worthy"],
    description: "Gourmet burgers with creative combinations and premium ingredients.",
    specialties: ["Wagyu Burger", "Truffle Fries", "Craft Beer"],
    coordinates: { lat: 3.0738, lng: 101.6065 },
    ratings: {
      taste: 4.3,
      environment: 4.4,
      service: 4.0
    }
  },
  {
    id: 5,
    name: "Sushi Zen",
    cuisine: "japanese",
    region: "kl-city",
    area: "Kuala Lumpur",
    address: "KLCC, Kuala Lumpur",
    phone: "+603-2382-1111",
    image: "https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=400&h=300&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1553621042-f6e147245754?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1611143669185-af224c5e3252?w=800&h=600&fit=crop"
    ],
    rating: 4.6,
    reviewCount: 743,
    priceRange: "RM 80-200",
    isHalal: false,
    isOpen: true,
    openingHours: {
      monday: "6:00 PM - 12:00 AM",
      tuesday: "6:00 PM - 12:00 AM",
      wednesday: "6:00 PM - 12:00 AM",
      thursday: "6:00 PM - 12:00 AM",
      friday: "6:00 PM - 1:00 AM",
      saturday: "6:00 PM - 1:00 AM",
      sunday: "6:00 PM - 12:00 AM"
    },
    badges: ["high-rating", "premium", "omakase"],
    occasions: ["date-night", "celebration", "business-lunch"],
    conditions: ["wifi", "private-room", "parking"],
    description: "Premium sushi restaurant with fresh ingredients flown in from Japan.",
    specialties: ["Omakase", "Toro Sashimi", "Uni"],
    coordinates: { lat: 3.1578, lng: 101.7123 },
    ratings: {
      taste: 4.7,
      environment: 4.6,
      service: 4.5
    }
  },

  // 新增餐厅数据
  {
    id: 6,
    name: "Penang Road Famous Teochew Chendul",
    cuisine: "chinese",
    region: "penang",
    area: "Penang",
    address: "27 & 29, Lebuh Keng Kwee, George Town, Penang",
    phone: "+************",
    image: "https://images.unsplash.com/photo-1551024506-0bccd828d307?w=400&h=300&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1551024506-0bccd828d307?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1563805042-7684c019e1cb?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop"
    ],
    rating: 4.4,
    reviewCount: 2156,
    priceRange: "RM 3-8",
    isHalal: false,
    isOpen: true,
    openingHours: {
      monday: "10:00 AM - 6:00 PM",
      tuesday: "10:00 AM - 6:00 PM",
      wednesday: "10:00 AM - 6:00 PM",
      thursday: "10:00 AM - 6:00 PM",
      friday: "10:00 AM - 6:00 PM",
      saturday: "10:00 AM - 6:00 PM",
      sunday: "10:00 AM - 6:00 PM"
    },
    badges: ["popular", "local-favorite", "heritage"],
    occasions: ["quick-bite", "casual-hangout"],
    conditions: ["outdoor-seating"],
    description: "Famous heritage dessert stall serving traditional Teochew chendul since 1936.",
    specialties: ["Chendul", "Ice Kacang", "Red Bean Ice"],
    coordinates: { lat: 5.4164, lng: 100.3327 },
    ratings: {
      taste: 4.6,
      environment: 3.8,
      service: 4.2
    }
  },

  {
    id: 7,
    name: "Atmosphere 360",
    cuisine: "western",
    region: "kl-city",
    area: "Kuala Lumpur",
    address: "KL Tower, No. 2 Jalan Punchak, Kuala Lumpur",
    phone: "+603-2020-5055",
    image: "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=400&h=300&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1551218808-94e220e084d2?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1559339352-11d035aa65de?w=800&h=600&fit=crop"
    ],
    rating: 4.2,
    reviewCount: 1834,
    priceRange: "RM 120-300",
    isHalal: true,
    isOpen: true,
    openingHours: {
      monday: "6:00 PM - 12:00 AM",
      tuesday: "6:00 PM - 12:00 AM",
      wednesday: "6:00 PM - 12:00 AM",
      thursday: "6:00 PM - 12:00 AM",
      friday: "6:00 PM - 1:00 AM",
      saturday: "6:00 PM - 1:00 AM",
      sunday: "6:00 PM - 12:00 AM"
    },
    badges: ["premium", "halal", "scenic-view"],
    occasions: ["date-night", "celebration", "business-lunch"],
    conditions: ["halal", "private-room", "parking", "wifi"],
    description: "Revolving restaurant with panoramic city views and international buffet.",
    specialties: ["International Buffet", "Grilled Lamb", "Seafood Platter"],
    coordinates: { lat: 3.1530, lng: 101.7031 },
    ratings: {
      taste: 4.0,
      environment: 4.8,
      service: 4.1
    }
  },

  {
    id: 8,
    name: "Jalan Alor Food Street",
    cuisine: "fusion",
    region: "kl-city",
    area: "Kuala Lumpur",
    address: "Jalan Alor, Bukit Bintang, Kuala Lumpur",
    phone: "+603-2148-8888",
    image: "https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=400&h=300&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop"
    ],
    rating: 4.3,
    reviewCount: 3421,
    priceRange: "RM 8-25",
    isHalal: false,
    isOpen: true,
    openingHours: {
      monday: "6:00 PM - 2:00 AM",
      tuesday: "6:00 PM - 2:00 AM",
      wednesday: "6:00 PM - 2:00 AM",
      thursday: "6:00 PM - 2:00 AM",
      friday: "6:00 PM - 3:00 AM",
      saturday: "6:00 PM - 3:00 AM",
      sunday: "6:00 PM - 2:00 AM"
    },
    badges: ["popular", "street-food", "late-night"],
    occasions: ["late-night", "casual-hangout"],
    conditions: ["outdoor-seating", "live-music"],
    description: "Iconic street food destination with diverse local and international cuisines.",
    specialties: ["BBQ Seafood", "Hokkien Mee", "Satay"],
    coordinates: { lat: 3.1466, lng: 101.7101 },
    ratings: {
      taste: 4.4,
      environment: 4.0,
      service: 4.1
    }
  },

  {
    id: 9,
    name: "Restoran Rebung Chef Ismail",
    cuisine: "malay",
    region: "kl-city",
    area: "Kuala Lumpur",
    address: "Jalan Ampang, Kuala Lumpur",
    phone: "+603-4251-3833",
    image: "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400&h=300&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=800&h=600&fit=crop"
    ],
    rating: 4.5,
    reviewCount: 1876,
    priceRange: "RM 25-60",
    isHalal: true,
    isOpen: true,
    openingHours: {
      monday: "12:00 PM - 3:00 PM, 7:00 PM - 11:00 PM",
      tuesday: "12:00 PM - 3:00 PM, 7:00 PM - 11:00 PM",
      wednesday: "12:00 PM - 3:00 PM, 7:00 PM - 11:00 PM",
      thursday: "12:00 PM - 3:00 PM, 7:00 PM - 11:00 PM",
      friday: "12:00 PM - 3:00 PM, 7:00 PM - 11:00 PM",
      saturday: "12:00 PM - 3:00 PM, 7:00 PM - 11:00 PM",
      sunday: "12:00 PM - 3:00 PM, 7:00 PM - 11:00 PM"
    },
    badges: ["halal", "chef-special", "authentic"],
    occasions: ["family-dinner", "celebration"],
    conditions: ["halal", "parking", "private-room"],
    description: "Celebrity chef's restaurant serving refined traditional Malay cuisine.",
    specialties: ["Rendang Tok", "Gulai Kawah", "Kerabu Mangga"],
    coordinates: { lat: 3.1619, lng: 101.7158 },
    ratings: {
      taste: 4.7,
      environment: 4.4,
      service: 4.4
    }
  },

  {
    id: 10,
    name: "Ganga Cafe",
    cuisine: "indian",
    region: "bangsar",
    area: "Bangsar",
    address: "Jalan Telawi 3, Bangsar Baru, Kuala Lumpur",
    phone: "+603-2287-6299",
    image: "https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=400&h=300&fit=crop",
    images: [
      "https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1574653853027-5d3ac9b9e7c7?w=800&h=600&fit=crop",
      "https://images.unsplash.com/photo-1585937421612-70a008356fbe?w=800&h=600&fit=crop"
    ],
    rating: 4.4,
    reviewCount: 1234,
    priceRange: "RM 15-35",
    isHalal: false,
    isOpen: true,
    openingHours: {
      monday: "7:00 AM - 11:00 PM",
      tuesday: "7:00 AM - 11:00 PM",
      wednesday: "7:00 AM - 11:00 PM",
      thursday: "7:00 AM - 11:00 PM",
      friday: "7:00 AM - 11:00 PM",
      saturday: "7:00 AM - 11:00 PM",
      sunday: "7:00 AM - 11:00 PM"
    },
    badges: ["vegetarian-friendly", "authentic"],
    occasions: ["casual-hangout", "family-dinner"],
    conditions: ["vegetarian", "wifi", "outdoor-seating"],
    description: "Popular vegetarian Indian restaurant with authentic South Indian flavors.",
    specialties: ["Masala Dosa", "Thali Set", "Mango Lassi"],
    coordinates: { lat: 3.1319, lng: 101.6741 },
    ratings: {
      taste: 4.5,
      environment: 4.2,
      service: 4.5
    }
  }
];

// Mock Reviews Data
export const reviews = [
  {
    id: 1,
    restaurantId: 1,
    userId: 1,
    userName: "Ahmad Rahman",
    userAvatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100",
    rating: 5,
    ratings: { taste: 5, environment: 4, service: 5 },
    title: "Best Nasi Lemak in KL!",
    content: "This place serves the most authentic nasi lemak I've ever tasted. The sambal is perfectly spicy and the coconut rice is fragrant. Definitely coming back!",
    images: [
      "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300",
      "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300"
    ],
    date: "2024-01-15",
    helpful: 23,
    replies: []
  },
  {
    id: 2,
    restaurantId: 1,
    userId: 2,
    userName: "Sarah Lim",
    userAvatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100",
    rating: 4,
    ratings: { taste: 4, environment: 4, service: 4 },
    title: "Good but crowded",
    content: "Food is great but the place gets very crowded during peak hours. Worth the wait though!",
    images: [],
    date: "2024-01-10",
    helpful: 12,
    replies: [
      {
        id: 1,
        userId: 1,
        userName: "Ahmad Rahman",
        content: "Try going early morning, less crowded!",
        date: "2024-01-11"
      }
    ]
  }
];

// Mock Users Data
export const users = [
  {
    id: 1,
    name: "Ahmad Rahman",
    email: "<EMAIL>",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100",
    level: "Food Explorer",
    reviewCount: 45,
    followers: 123,
    following: 67,
    favorites: [1, 3, 5],
    joinDate: "2023-06-15"
  },
  {
    id: 2,
    name: "Sarah Lim",
    email: "<EMAIL>",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100",
    level: "Foodie",
    reviewCount: 28,
    followers: 89,
    following: 45,
    favorites: [1, 2, 4],
    joinDate: "2023-08-22"
  }
];

// Badge definitions
export const badges = {
  'popular': { name: 'Popular', icon: '🔥', color: '#e74c3c' },
  'halal': { name: 'Halal', icon: '✅', color: '#27ae60' },
  'local-favorite': { name: 'Local Favorite', icon: '❤️', color: '#e91e63' },
  'high-rating': { name: 'High Rating', icon: '⭐', color: '#f39c12' },
  'authentic': { name: 'Authentic', icon: '🏆', color: '#9b59b6' },
  'instagram-worthy': { name: 'Instagram Worthy', icon: '📸', color: '#3498db' },
  'premium': { name: 'Premium', icon: '💎', color: '#34495e' },
  'omakase': { name: 'Omakase', icon: '🍣', color: '#e67e22' }
};

// Language translations
export const translations = {
  en: {
    home: 'Home',
    search: 'Search',
    rankings: 'Rankings',
    map: 'Map',
    profile: 'Profile',
    login: 'Login',
    register: 'Register',
    logout: 'Logout',
    searchPlaceholder: 'Search restaurants, cuisines...',
    featuredRestaurants: 'Featured Restaurants',
    topRated: 'Top Rated',
    nearYou: 'Near You',
    categories: 'Categories',
    viewAll: 'View All',
    reviews: 'Reviews',
    writeReview: 'Write Review',
    rating: 'Rating',
    taste: 'Taste',
    environment: 'Environment',
    service: 'Service',
    openNow: 'Open Now',
    closed: 'Closed',
    halal: 'Halal',
    priceRange: 'Price Range'
  },
  zh: {
    home: '首页',
    search: '搜索',
    rankings: '排行榜',
    map: '地图',
    profile: '个人资料',
    login: '登录',
    register: '注册',
    logout: '登出',
    searchPlaceholder: '搜索餐厅、菜系...',
    featuredRestaurants: '精选餐厅',
    topRated: '高评分',
    nearYou: '附近',
    categories: '分类',
    viewAll: '查看全部',
    reviews: '评论',
    writeReview: '写评论',
    rating: '评分',
    taste: '味道',
    environment: '环境',
    service: '服务',
    openNow: '营业中',
    closed: '已关闭',
    halal: '清真',
    priceRange: '价格范围'
  },
  ms: {
    home: 'Laman Utama',
    search: 'Cari',
    rankings: 'Kedudukan',
    map: 'Peta',
    profile: 'Profil',
    login: 'Log Masuk',
    register: 'Daftar',
    logout: 'Log Keluar',
    searchPlaceholder: 'Cari restoran, masakan...',
    featuredRestaurants: 'Restoran Pilihan',
    topRated: 'Rating Tinggi',
    nearYou: 'Berhampiran',
    categories: 'Kategori',
    viewAll: 'Lihat Semua',
    reviews: 'Ulasan',
    writeReview: 'Tulis Ulasan',
    rating: 'Penilaian',
    taste: 'Rasa',
    environment: 'Persekitaran',
    service: 'Perkhidmatan',
    openNow: 'Buka Sekarang',
    closed: 'Tutup',
    halal: 'Halal',
    priceRange: 'Julat Harga'
  }
};

// Utility functions for data manipulation
export function getRestaurantById(id) {
  return restaurants.find(restaurant => restaurant.id === parseInt(id));
}

export function getRestaurantsByCategory(category) {
  return restaurants.filter(restaurant => restaurant.cuisine === category);
}

export function getRestaurantsByArea(area) {
  return restaurants.filter(restaurant => restaurant.area === area);
}

export function searchRestaurants(query, filters = {}) {
  let results = restaurants;
  
  // Text search
  if (query) {
    const searchTerm = query.toLowerCase();
    results = results.filter(restaurant => 
      restaurant.name.toLowerCase().includes(searchTerm) ||
      restaurant.cuisine.toLowerCase().includes(searchTerm) ||
      restaurant.specialties.some(specialty => 
        specialty.toLowerCase().includes(searchTerm)
      )
    );
  }
  
  // Apply filters
  if (filters.cuisine && filters.cuisine !== 'all') {
    results = results.filter(restaurant => restaurant.cuisine === filters.cuisine);
  }
  
  if (filters.area && filters.area !== 'all') {
    results = results.filter(restaurant => restaurant.area === filters.area);
  }
  
  if (filters.halal) {
    results = results.filter(restaurant => restaurant.isHalal);
  }
  
  if (filters.rating) {
    results = results.filter(restaurant => restaurant.rating >= filters.rating);
  }
  
  // Sort results
  if (filters.sortBy) {
    switch (filters.sortBy) {
      case 'rating':
        results.sort((a, b) => b.rating - a.rating);
        break;
      case 'reviews':
        results.sort((a, b) => b.reviewCount - a.reviewCount);
        break;
      case 'name':
        results.sort((a, b) => a.name.localeCompare(b.name));
        break;
    }
  }
  
  return results;
}

export function getReviewsByRestaurant(restaurantId) {
  return reviews.filter(review => review.restaurantId === parseInt(restaurantId));
}

export function getUserById(id) {
  return users.find(user => user.id === parseInt(id));
}

// Additional Helper Functions for New Category System
export function getRestaurantsByRegion(regionId) {
  return restaurants.filter(restaurant => restaurant.region === regionId);
}

export function getRestaurantsByOccasion(occasionId) {
  return restaurants.filter(restaurant =>
    restaurant.occasions && restaurant.occasions.includes(occasionId)
  );
}

export function getRestaurantsByCondition(conditionId) {
  return restaurants.filter(restaurant =>
    restaurant.conditions && restaurant.conditions.includes(conditionId)
  );
}
