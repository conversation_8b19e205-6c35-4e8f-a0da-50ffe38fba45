// EatMY - Mock Data

// Restaurant Categories
export const categories = [
  { id: 'malay', name: 'Malay', icon: '🍛', color: '#e74c3c' },
  { id: 'chinese', name: 'Chinese', icon: '🥢', color: '#f39c12' },
  { id: 'indian', name: 'Indian', icon: '🍛', color: '#e67e22' },
  { id: 'western', name: 'Western', icon: '🍔', color: '#3498db' },
  { id: 'japanese', name: 'Japanese', icon: '🍣', color: '#9b59b6' },
  { id: 'korean', name: 'Korean', icon: '🍜', color: '#e91e63' },
  { id: 'thai', name: 'Thai', icon: '🌶️', color: '#4caf50' },
  { id: 'fusion', name: 'Fusion', icon: '🍽️', color: '#ff9800' }
];

// Malaysian States/Areas
export const areas = [
  'Kuala Lumpur', 'Selangor', 'Penang', 'Jo<PERSON>', 'Perak', 'Kedah', 
  'Kelantan', 'Terengganu', 'Pahang', 'Negeri Sembilan', 'Mel<PERSON>', 
  'Perlis', 'Sabah', 'Sarawak', 'Labuan', 'Putrajaya'
];

// Mock Restaurants Data
export const restaurants = [
  {
    id: 1,
    name: "Nasi Lemak Wanjo",
    cuisine: "malay",
    area: "Kuala Lumpur",
    address: "Jalan Tun Tan Cheng Lock, Kuala Lumpur",
    phone: "+603-2078-3813",
    image: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400",
    images: [
      "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800",
      "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=800",
      "https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=800"
    ],
    rating: 4.5,
    reviewCount: 1247,
    priceRange: "RM 5-15",
    isHalal: true,
    isOpen: true,
    openingHours: {
      monday: "6:00 AM - 2:00 PM",
      tuesday: "6:00 AM - 2:00 PM",
      wednesday: "6:00 AM - 2:00 PM",
      thursday: "6:00 AM - 2:00 PM",
      friday: "6:00 AM - 2:00 PM",
      saturday: "6:00 AM - 2:00 PM",
      sunday: "Closed"
    },
    badges: ["popular", "halal", "local-favorite"],
    description: "Famous for authentic nasi lemak with perfectly cooked coconut rice and spicy sambal.",
    specialties: ["Nasi Lemak", "Rendang", "Sambal Sotong"],
    coordinates: { lat: 3.1390, lng: 101.6869 },
    ratings: {
      taste: 4.6,
      environment: 4.2,
      service: 4.4
    }
  },
  {
    id: 2,
    name: "Dim Sum Palace",
    cuisine: "chinese",
    area: "Kuala Lumpur",
    address: "Jalan Petaling, Chinatown, KL",
    phone: "+603-2072-1234",
    image: "https://images.unsplash.com/photo-1563379091339-03246963d96a?w=400",
    images: [
      "https://images.unsplash.com/photo-1563379091339-03246963d96a?w=800",
      "https://images.unsplash.com/photo-1496116218417-1a781b1c416c?w=800"
    ],
    rating: 4.3,
    reviewCount: 892,
    priceRange: "RM 15-40",
    isHalal: false,
    isOpen: true,
    openingHours: {
      monday: "7:00 AM - 3:00 PM",
      tuesday: "7:00 AM - 3:00 PM",
      wednesday: "7:00 AM - 3:00 PM",
      thursday: "7:00 AM - 3:00 PM",
      friday: "7:00 AM - 3:00 PM",
      saturday: "7:00 AM - 4:00 PM",
      sunday: "7:00 AM - 4:00 PM"
    },
    badges: ["high-rating"],
    description: "Traditional dim sum restaurant serving handmade dumplings and tea.",
    specialties: ["Har Gow", "Siu Mai", "Char Siu Bao"],
    coordinates: { lat: 3.1478, lng: 101.6953 },
    ratings: {
      taste: 4.4,
      environment: 4.1,
      service: 4.4
    }
  },
  {
    id: 3,
    name: "Banana Leaf Restaurant",
    cuisine: "indian",
    area: "Kuala Lumpur",
    address: "Brickfields, Little India, KL",
    phone: "+603-2274-5678",
    image: "https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=400",
    images: [
      "https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=800",
      "https://images.unsplash.com/photo-1574653853027-5d3ac9b9e7c7?w=800"
    ],
    rating: 4.4,
    reviewCount: 654,
    priceRange: "RM 12-25",
    isHalal: true,
    isOpen: false,
    openingHours: {
      monday: "11:00 AM - 10:00 PM",
      tuesday: "11:00 AM - 10:00 PM",
      wednesday: "11:00 AM - 10:00 PM",
      thursday: "11:00 AM - 10:00 PM",
      friday: "11:00 AM - 10:00 PM",
      saturday: "11:00 AM - 11:00 PM",
      sunday: "11:00 AM - 11:00 PM"
    },
    badges: ["halal", "authentic"],
    description: "Authentic South Indian cuisine served on traditional banana leaves.",
    specialties: ["Fish Curry", "Mutton Varuval", "Thosai"],
    coordinates: { lat: 3.1337, lng: 101.6869 },
    ratings: {
      taste: 4.5,
      environment: 4.2,
      service: 4.5
    }
  },
  {
    id: 4,
    name: "The Burger Lab",
    cuisine: "western",
    area: "Selangor",
    address: "Sunway Pyramid, Petaling Jaya",
    phone: "+603-5621-9876",
    image: "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400",
    images: [
      "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=800",
      "https://images.unsplash.com/photo-1550547660-d9450f859349?w=800"
    ],
    rating: 4.2,
    reviewCount: 1156,
    priceRange: "RM 20-45",
    isHalal: false,
    isOpen: true,
    openingHours: {
      monday: "11:00 AM - 10:00 PM",
      tuesday: "11:00 AM - 10:00 PM",
      wednesday: "11:00 AM - 10:00 PM",
      thursday: "11:00 AM - 10:00 PM",
      friday: "11:00 AM - 11:00 PM",
      saturday: "11:00 AM - 11:00 PM",
      sunday: "11:00 AM - 10:00 PM"
    },
    badges: ["popular", "instagram-worthy"],
    description: "Gourmet burgers with creative combinations and premium ingredients.",
    specialties: ["Wagyu Burger", "Truffle Fries", "Craft Beer"],
    coordinates: { lat: 3.0738, lng: 101.6065 },
    ratings: {
      taste: 4.3,
      environment: 4.4,
      service: 4.0
    }
  },
  {
    id: 5,
    name: "Sushi Zen",
    cuisine: "japanese",
    area: "Kuala Lumpur",
    address: "KLCC, Kuala Lumpur",
    phone: "+603-2382-1111",
    image: "https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=400",
    images: [
      "https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=800",
      "https://images.unsplash.com/photo-1553621042-f6e147245754?w=800"
    ],
    rating: 4.6,
    reviewCount: 743,
    priceRange: "RM 80-200",
    isHalal: false,
    isOpen: true,
    openingHours: {
      monday: "6:00 PM - 12:00 AM",
      tuesday: "6:00 PM - 12:00 AM",
      wednesday: "6:00 PM - 12:00 AM",
      thursday: "6:00 PM - 12:00 AM",
      friday: "6:00 PM - 1:00 AM",
      saturday: "6:00 PM - 1:00 AM",
      sunday: "6:00 PM - 12:00 AM"
    },
    badges: ["high-rating", "premium", "omakase"],
    description: "Premium sushi restaurant with fresh ingredients flown in from Japan.",
    specialties: ["Omakase", "Toro Sashimi", "Uni"],
    coordinates: { lat: 3.1578, lng: 101.7123 },
    ratings: {
      taste: 4.7,
      environment: 4.6,
      service: 4.5
    }
  }
];

// Mock Reviews Data
export const reviews = [
  {
    id: 1,
    restaurantId: 1,
    userId: 1,
    userName: "Ahmad Rahman",
    userAvatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100",
    rating: 5,
    ratings: { taste: 5, environment: 4, service: 5 },
    title: "Best Nasi Lemak in KL!",
    content: "This place serves the most authentic nasi lemak I've ever tasted. The sambal is perfectly spicy and the coconut rice is fragrant. Definitely coming back!",
    images: [
      "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300",
      "https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=300"
    ],
    date: "2024-01-15",
    helpful: 23,
    replies: []
  },
  {
    id: 2,
    restaurantId: 1,
    userId: 2,
    userName: "Sarah Lim",
    userAvatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100",
    rating: 4,
    ratings: { taste: 4, environment: 4, service: 4 },
    title: "Good but crowded",
    content: "Food is great but the place gets very crowded during peak hours. Worth the wait though!",
    images: [],
    date: "2024-01-10",
    helpful: 12,
    replies: [
      {
        id: 1,
        userId: 1,
        userName: "Ahmad Rahman",
        content: "Try going early morning, less crowded!",
        date: "2024-01-11"
      }
    ]
  }
];

// Mock Users Data
export const users = [
  {
    id: 1,
    name: "Ahmad Rahman",
    email: "<EMAIL>",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100",
    level: "Food Explorer",
    reviewCount: 45,
    followers: 123,
    following: 67,
    favorites: [1, 3, 5],
    joinDate: "2023-06-15"
  },
  {
    id: 2,
    name: "Sarah Lim",
    email: "<EMAIL>",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100",
    level: "Foodie",
    reviewCount: 28,
    followers: 89,
    following: 45,
    favorites: [1, 2, 4],
    joinDate: "2023-08-22"
  }
];

// Badge definitions
export const badges = {
  'popular': { name: 'Popular', icon: '🔥', color: '#e74c3c' },
  'halal': { name: 'Halal', icon: '✅', color: '#27ae60' },
  'local-favorite': { name: 'Local Favorite', icon: '❤️', color: '#e91e63' },
  'high-rating': { name: 'High Rating', icon: '⭐', color: '#f39c12' },
  'authentic': { name: 'Authentic', icon: '🏆', color: '#9b59b6' },
  'instagram-worthy': { name: 'Instagram Worthy', icon: '📸', color: '#3498db' },
  'premium': { name: 'Premium', icon: '💎', color: '#34495e' },
  'omakase': { name: 'Omakase', icon: '🍣', color: '#e67e22' }
};

// Language translations
export const translations = {
  en: {
    home: 'Home',
    search: 'Search',
    rankings: 'Rankings',
    map: 'Map',
    profile: 'Profile',
    login: 'Login',
    register: 'Register',
    logout: 'Logout',
    searchPlaceholder: 'Search restaurants, cuisines...',
    featuredRestaurants: 'Featured Restaurants',
    topRated: 'Top Rated',
    nearYou: 'Near You',
    categories: 'Categories',
    viewAll: 'View All',
    reviews: 'Reviews',
    writeReview: 'Write Review',
    rating: 'Rating',
    taste: 'Taste',
    environment: 'Environment',
    service: 'Service',
    openNow: 'Open Now',
    closed: 'Closed',
    halal: 'Halal',
    priceRange: 'Price Range'
  },
  zh: {
    home: '首页',
    search: '搜索',
    rankings: '排行榜',
    map: '地图',
    profile: '个人资料',
    login: '登录',
    register: '注册',
    logout: '登出',
    searchPlaceholder: '搜索餐厅、菜系...',
    featuredRestaurants: '精选餐厅',
    topRated: '高评分',
    nearYou: '附近',
    categories: '分类',
    viewAll: '查看全部',
    reviews: '评论',
    writeReview: '写评论',
    rating: '评分',
    taste: '味道',
    environment: '环境',
    service: '服务',
    openNow: '营业中',
    closed: '已关闭',
    halal: '清真',
    priceRange: '价格范围'
  },
  ms: {
    home: 'Laman Utama',
    search: 'Cari',
    rankings: 'Kedudukan',
    map: 'Peta',
    profile: 'Profil',
    login: 'Log Masuk',
    register: 'Daftar',
    logout: 'Log Keluar',
    searchPlaceholder: 'Cari restoran, masakan...',
    featuredRestaurants: 'Restoran Pilihan',
    topRated: 'Rating Tinggi',
    nearYou: 'Berhampiran',
    categories: 'Kategori',
    viewAll: 'Lihat Semua',
    reviews: 'Ulasan',
    writeReview: 'Tulis Ulasan',
    rating: 'Penilaian',
    taste: 'Rasa',
    environment: 'Persekitaran',
    service: 'Perkhidmatan',
    openNow: 'Buka Sekarang',
    closed: 'Tutup',
    halal: 'Halal',
    priceRange: 'Julat Harga'
  }
};

// Utility functions for data manipulation
export function getRestaurantById(id) {
  return restaurants.find(restaurant => restaurant.id === parseInt(id));
}

export function getRestaurantsByCategory(category) {
  return restaurants.filter(restaurant => restaurant.cuisine === category);
}

export function getRestaurantsByArea(area) {
  return restaurants.filter(restaurant => restaurant.area === area);
}

export function searchRestaurants(query, filters = {}) {
  let results = restaurants;
  
  // Text search
  if (query) {
    const searchTerm = query.toLowerCase();
    results = results.filter(restaurant => 
      restaurant.name.toLowerCase().includes(searchTerm) ||
      restaurant.cuisine.toLowerCase().includes(searchTerm) ||
      restaurant.specialties.some(specialty => 
        specialty.toLowerCase().includes(searchTerm)
      )
    );
  }
  
  // Apply filters
  if (filters.cuisine && filters.cuisine !== 'all') {
    results = results.filter(restaurant => restaurant.cuisine === filters.cuisine);
  }
  
  if (filters.area && filters.area !== 'all') {
    results = results.filter(restaurant => restaurant.area === filters.area);
  }
  
  if (filters.halal) {
    results = results.filter(restaurant => restaurant.isHalal);
  }
  
  if (filters.rating) {
    results = results.filter(restaurant => restaurant.rating >= filters.rating);
  }
  
  // Sort results
  if (filters.sortBy) {
    switch (filters.sortBy) {
      case 'rating':
        results.sort((a, b) => b.rating - a.rating);
        break;
      case 'reviews':
        results.sort((a, b) => b.reviewCount - a.reviewCount);
        break;
      case 'name':
        results.sort((a, b) => a.name.localeCompare(b.name));
        break;
    }
  }
  
  return results;
}

export function getReviewsByRestaurant(restaurantId) {
  return reviews.filter(review => review.restaurantId === parseInt(restaurantId));
}

export function getUserById(id) {
  return users.find(user => user.id === parseInt(id));
}
