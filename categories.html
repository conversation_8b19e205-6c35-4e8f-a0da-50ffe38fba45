<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browse Categories - EatMY</title>
    <meta name="description" content="Explore restaurants by cuisine categories. Find your favorite Malaysian, Chinese, Indian, Western and more food options.">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/pages.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="index.html" class="nav-brand">EatMY</a>
            
            <ul class="nav-links">
                <li><a href="index.html" class="nav-link" data-translate="home">Home</a></li>
                <li><a href="search.html" class="nav-link" data-translate="search">Search</a></li>
                <li><a href="rankings.html" class="nav-link" data-translate="rankings">Rankings</a></li>
                <li><a href="categories.html" class="nav-link active" data-translate="categories">Categories</a></li>
                <li><a href="map.html" class="nav-link" data-translate="map">Map</a></li>
            </ul>
            
            <div class="nav-actions">
                <!-- Language Switcher -->
                <div class="language-switcher">
                    <button class="language-button">
                        <span>EN</span>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                    </button>
                    <div class="language-dropdown">
                        <button class="language-option" data-language="en">English</button>
                        <button class="language-option" data-language="zh">中文</button>
                        <button class="language-option" data-language="ms">Bahasa</button>
                    </div>
                </div>
                
                <!-- User Menu -->
                <div class="user-menu" style="display: none;">
                    <a href="profile.html" class="profile-link btn btn-outline">Profile</a>
                    <button onclick="EatMY.logout()" class="btn btn-secondary" data-translate="logout">Logout</button>
                </div>
                
                <!-- Login Button -->
                <a href="login.html" class="login-btn btn btn-primary" data-translate="login">Login</a>
            </div>
            
            <button class="nav-toggle">☰</button>
        </nav>
    </header>

    <!-- Main Content -->
    <main style="margin-top: var(--header-height);">
        <!-- Page Header -->
        <section class="page-header section" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); color: var(--white);">
            <div class="container text-center">
                <h1 style="font-size: var(--text-4xl); font-weight: 700; margin-bottom: var(--space-4); color: var(--white);">
                    Explore by Categories
                </h1>
                <p style="font-size: var(--text-lg); color: rgba(255, 255, 255, 0.9); max-width: 600px; margin: 0 auto;">
                    Discover amazing restaurants organized by cuisine type. From traditional Malaysian dishes to international flavors.
                </p>
            </div>
        </section>

        <!-- Categories Navigation -->
        <section class="categories-nav section-sm" style="background: var(--white); border-bottom: 1px solid var(--gray-200);">
            <div class="container">
                <div class="category-tabs" style="display: flex; gap: var(--space-2); overflow-x: auto; padding-bottom: var(--space-2);">
                    <button class="category-tab active" data-category="cuisine">🍽️ Cuisine Types</button>
                    <button class="category-tab" data-category="regions">📍 Regions</button>
                    <button class="category-tab" data-category="occasions">🎉 Occasions</button>
                    <button class="category-tab" data-category="conditions">✨ Special Features</button>
                </div>
            </div>
        </section>

        <!-- Categories Grid -->
        <section class="categories-section section">
            <div class="container">
                <!-- Cuisine Categories -->
                <div class="category-section active" id="cuisine-section">
                    <div class="section-header text-center" style="margin-bottom: var(--space-12);">
                        <h2>Browse by Cuisine Type</h2>
                        <p style="color: var(--gray-600);">Discover restaurants by your favorite cuisine</p>
                    </div>
                    <div class="categories-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8" id="cuisine-grid">
                        <!-- Cuisine categories will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Regions Categories -->
                <div class="category-section" id="regions-section" style="display: none;">
                    <div class="section-header text-center" style="margin-bottom: var(--space-12);">
                        <h2>Browse by Region</h2>
                        <p style="color: var(--gray-600);">Find great food in popular Malaysian locations</p>
                    </div>
                    <div class="categories-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6" id="regions-grid">
                        <!-- Region categories will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Occasions Categories -->
                <div class="category-section" id="occasions-section" style="display: none;">
                    <div class="section-header text-center" style="margin-bottom: var(--space-12);">
                        <h2>Browse by Occasion</h2>
                        <p style="color: var(--gray-600);">Find the perfect restaurant for any occasion</p>
                    </div>
                    <div class="categories-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="occasions-grid">
                        <!-- Occasion categories will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Conditions Categories -->
                <div class="category-section" id="conditions-section" style="display: none;">
                    <div class="section-header text-center" style="margin-bottom: var(--space-12);">
                        <h2>Browse by Special Features</h2>
                        <p style="color: var(--gray-600);">Find restaurants with specific amenities and features</p>
                    </div>
                    <div class="categories-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="conditions-grid">
                        <!-- Condition categories will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured by Category -->
        <section class="featured-by-category section" style="background: var(--gray-50);">
            <div class="container">
                <h2 style="text-align: center; margin-bottom: var(--space-12);">Featured Restaurants by Category</h2>
                
                <div class="category-sections" id="category-sections">
                    <!-- Category sections will be populated by JavaScript -->
                </div>
            </div>
        </section>

        <!-- Popular Combinations -->
        <section class="popular-combinations section">
            <div class="container">
                <h2 style="text-align: center; margin-bottom: var(--space-8);">Popular Food Combinations</h2>
                <p style="text-align: center; color: var(--gray-600); margin-bottom: var(--space-12);">
                    Discover restaurants that serve multiple cuisines
                </p>
                
                <div class="combinations-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="combination-card card" style="text-align: center; padding: var(--space-8);">
                        <div style="font-size: 48px; margin-bottom: var(--space-4);">🍛🥢</div>
                        <h3 style="margin-bottom: var(--space-2);">Malay + Chinese</h3>
                        <p style="color: var(--gray-600); margin-bottom: var(--space-4);">
                            Experience the best of both worlds with restaurants serving traditional Malay and Chinese dishes
                        </p>
                        <a href="search.html?combination=malay-chinese" class="btn btn-outline">Explore</a>
                    </div>
                    
                    <div class="combination-card card" style="text-align: center; padding: var(--space-8);">
                        <div style="font-size: 48px; margin-bottom: var(--space-4);">🍛🍔</div>
                        <h3 style="margin-bottom: var(--space-2);">Local + Western</h3>
                        <p style="color: var(--gray-600); margin-bottom: var(--space-4);">
                            Fusion restaurants blending local Malaysian flavors with Western cooking styles
                        </p>
                        <a href="search.html?combination=local-western" class="btn btn-outline">Explore</a>
                    </div>
                    
                    <div class="combination-card card" style="text-align: center; padding: var(--space-8);">
                        <div style="font-size: 48px; margin-bottom: var(--space-4);">🍣🍜</div>
                        <h3 style="margin-bottom: var(--space-2);">Japanese + Korean</h3>
                        <p style="color: var(--gray-600); margin-bottom: var(--space-4);">
                            Asian fusion restaurants offering the best of Japanese and Korean cuisines
                        </p>
                        <a href="search.html?combination=japanese-korean" class="btn btn-outline">Explore</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Dietary Preferences -->
        <section class="dietary-preferences section" style="background: var(--gray-50);">
            <div class="container">
                <h2 style="text-align: center; margin-bottom: var(--space-8);">Dietary Preferences</h2>
                <p style="text-align: center; color: var(--gray-600); margin-bottom: var(--space-12);">
                    Find restaurants that cater to your specific dietary needs
                </p>
                
                <div class="dietary-grid grid grid-cols-2 md:grid-cols-4 gap-6">
                    <a href="search.html?halal=true" class="dietary-card card" style="text-align: center; padding: var(--space-6); text-decoration: none; color: inherit;">
                        <div style="font-size: 36px; margin-bottom: var(--space-3);">✅</div>
                        <h4 style="margin-bottom: var(--space-2);">Halal</h4>
                        <p style="color: var(--gray-600); font-size: var(--text-sm);">Certified halal restaurants</p>
                    </a>
                    
                    <a href="search.html?vegetarian=true" class="dietary-card card" style="text-align: center; padding: var(--space-6); text-decoration: none; color: inherit;">
                        <div style="font-size: 36px; margin-bottom: var(--space-3);">🥬</div>
                        <h4 style="margin-bottom: var(--space-2);">Vegetarian</h4>
                        <p style="color: var(--gray-600); font-size: var(--text-sm);">Plant-based options</p>
                    </a>
                    
                    <a href="search.html?vegan=true" class="dietary-card card" style="text-align: center; padding: var(--space-6); text-decoration: none; color: inherit;">
                        <div style="font-size: 36px; margin-bottom: var(--space-3);">🌱</div>
                        <h4 style="margin-bottom: var(--space-2);">Vegan</h4>
                        <p style="color: var(--gray-600); font-size: var(--text-sm);">100% plant-based</p>
                    </a>
                    
                    <a href="search.html?gluten-free=true" class="dietary-card card" style="text-align: center; padding: var(--space-6); text-decoration: none; color: inherit;">
                        <div style="font-size: 36px; margin-bottom: var(--space-3);">🌾</div>
                        <h4 style="margin-bottom: var(--space-2);">Gluten-Free</h4>
                        <p style="color: var(--gray-600); font-size: var(--text-sm);">Gluten-free options</p>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer" style="background: var(--gray-900); color: var(--white); padding: var(--space-16) 0;">
        <div class="container">
            <div class="footer-content grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="footer-section">
                    <h3 class="footer-title">EatMY</h3>
                    <p class="footer-description">Malaysia's premier food review and restaurant discovery platform</p>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-heading">Explore</h4>
                    <ul class="footer-links">
                        <li><a href="restaurants.html">All Restaurants</a></li>
                        <li><a href="categories.html">Categories</a></li>
                        <li><a href="rankings.html">Top Rankings</a></li>
                        <li><a href="map.html">Map View</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-heading">Community</h4>
                    <ul class="footer-links">
                        <li><a href="review.html">Write a Review</a></li>
                        <li><a href="profile.html">My Profile</a></li>
                        <li><a href="help.html">Help Center</a></li>
                        <li><a href="about.html">About Us</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4 class="footer-heading">Connect</h4>
                    <div class="social-links">
                        <a href="#" class="social-link">Facebook</a>
                        <a href="#" class="social-link">Instagram</a>
                        <a href="#" class="social-link">Twitter</a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom" style="border-top: 1px solid var(--gray-700); margin-top: var(--space-8); padding-top: var(--space-8); text-center;">
                <p>&copy; 2024 EatMY. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <!-- 加载数据文件（必须先加载） -->
    <script src="js/data.js"></script>
    <!-- 加载应用逻辑 -->
    <script src="js/main.js"></script>
    <script src="js/categories.js"></script>
</body>
</html>
